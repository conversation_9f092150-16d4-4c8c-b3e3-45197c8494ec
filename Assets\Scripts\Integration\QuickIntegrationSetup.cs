using UnityEngine;
using Sirenix.OdinInspector;
using Animation.StateSync;
using DefaultNamespace.Mono.Interface;
using Module.Mono.Animancer.RealsticFemale;

namespace Integration
{
    /// <summary>
    /// Quick setup script for immediate integration of Centralized State Synchronization
    /// Run this in your scene to quickly set up the system.
    /// </summary>
    public class QuickIntegrationSetup : MonoBehaviour
    {
        [InfoBox("This script provides one-click integration of the Centralized State Synchronization system. " +
                 "Click 'Quick Setup' to automatically configure your scene.", InfoMessageType.Info)]
        
        [But<PERSON>("Quick Setup - Integrate Now!", ButtonSizes.Large)]
        public void QuickSetup()
        {
            Debug.Log("=== STARTING QUICK INTEGRATION SETUP ===");
            
            try
            {
                // Step 1: Find player components
                var playerController = FindObjectOfType<PlayerController>();
                var characterParameters = FindObjectOfType<CharacterParameters>();
                
                if (playerController == null)
                {
                    Debug.LogError("❌ PlayerController not found in scene! Please ensure you have a player character.");
                    return;
                }
                
                if (characterParameters == null)
                {
                    Debug.LogError("❌ CharacterParameters not found in scene! Please ensure your player has CharacterParameters component.");
                    return;
                }
                
                Debug.Log("✅ Found PlayerController and CharacterParameters");
                
                // Step 2: Add CentralizedStateSynchronizer if not present
                var existingSynchronizer = playerController.GetComponentInChildren<CentralizedStateSynchronizer>();
                if (existingSynchronizer == null)
                {
                    var syncObject = new GameObject("CentralizedStateSynchronizer");
                    syncObject.transform.SetParent(playerController.transform);
                    var synchronizer = syncObject.AddComponent<CentralizedStateSynchronizer>();
                    
                    // Configure synchronizer
                    synchronizer.SetSynchronizationEnabled(true);
                    synchronizer.SetSynchronizationInterval(0.016f); // 60 FPS
                    
                    Debug.Log("✅ Added and configured CentralizedStateSynchronizer");
                }
                else
                {
                    Debug.Log("✅ CentralizedStateSynchronizer already exists");
                }
                
                // Step 3: Handle MovementModule replacement
                var existingMovementModule = playerController.GetComponentInChildren<RefactoredMovementModule>();
                var refactoredMovementModule = playerController.GetComponentInChildren<RefactoredMovementModule>();
                
                if (existingMovementModule != null && refactoredMovementModule == null)
                {
                    // Disable old movement module
                    existingMovementModule.enabled = false;
                    
                    // Add refactored movement module to the same GameObject
                    refactoredMovementModule = existingMovementModule.gameObject.AddComponent<RefactoredMovementModule>();
                    
                    Debug.Log("✅ Replaced MovementModule with RefactoredMovementModule");
                }
                else if (refactoredMovementModule != null)
                {
                    Debug.Log("✅ RefactoredMovementModule already exists");
                }
                else
                {
                    Debug.LogWarning("⚠️ No MovementModule found to replace");
                }
                
                // Step 4: Verify other modules
                var aimingModule = playerController.GetComponentInChildren<AimingModule>();
                var weaponModule = playerController.GetComponentInChildren<RefactoredMovementModule>();
                
                Debug.Log($"✅ AimingModule: {(aimingModule != null ? "Found" : "Not Found")}");
                Debug.Log($"✅ WeaponModule: {(weaponModule != null ? "Found" : "Not Found")}");
                
                // Step 5: Create integrator for ongoing management
                var existingIntegrator = FindObjectOfType<CentralizedStateSynchronizerIntegrator>();
                if (existingIntegrator == null)
                {
                    var integratorObject = new GameObject("SystemIntegrator");
                    var integrator = integratorObject.AddComponent<CentralizedStateSynchronizerIntegrator>();
                    Debug.Log("✅ Added CentralizedStateSynchronizerIntegrator for ongoing management");
                }
                
                Debug.Log("=== QUICK INTEGRATION SETUP COMPLETED SUCCESSFULLY ===");
                Debug.Log("🎉 Your Centralized State Synchronization system is now ready!");
                Debug.Log("💡 Use the SystemIntegrator GameObject for advanced configuration and testing.");
                
            }
            catch (System.Exception ex)
            {
                Debug.LogError($"❌ Quick setup failed: {ex.Message}");
                Debug.LogError("Please check the integration guide for manual setup instructions.");
            }
        }
        
        [Button("Verify Setup")]
        public void VerifySetup()
        {
            Debug.Log("=== VERIFYING INTEGRATION SETUP ===");
            
            var playerController = FindObjectOfType<PlayerController>();
            var characterParameters = FindObjectOfType<CharacterParameters>();
            var synchronizer = FindObjectOfType<CentralizedStateSynchronizer>();
            var refactoredMovement = FindObjectOfType<RefactoredMovementModule>();
            var integrator = FindObjectOfType<CentralizedStateSynchronizerIntegrator>();
            
            Debug.Log($"PlayerController: {(playerController != null ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"CharacterParameters: {(characterParameters != null ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"CentralizedStateSynchronizer: {(synchronizer != null ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"RefactoredMovementModule: {(refactoredMovement != null ? "✅ Found" : "❌ Missing")}");
            Debug.Log($"SystemIntegrator: {(integrator != null ? "✅ Found" : "❌ Missing")}");
            
            if (synchronizer != null)
            {
                var stats = synchronizer.GetSynchronizationStats();
                Debug.Log($"Synchronization Stats: {stats}");
            }
            
            bool isFullyIntegrated = playerController != null && characterParameters != null && 
                                   synchronizer != null && refactoredMovement != null;
            
            if (isFullyIntegrated)
            {
                Debug.Log("🎉 INTEGRATION VERIFICATION PASSED - System is ready to use!");
            }
            else
            {
                Debug.LogWarning("⚠️ Integration verification found missing components. Run Quick Setup again.");
            }
        }
        
        [Button("Test State Synchronization")]
        public void TestStateSynchronization()
        {
            var synchronizer = FindObjectOfType<CentralizedStateSynchronizer>();
            
            if (synchronizer == null)
            {
                Debug.LogWarning("❌ CentralizedStateSynchronizer not found! Run Quick Setup first.");
                return;
            }
            
            Debug.Log("=== TESTING STATE SYNCHRONIZATION ===");
            
            // Get current state
            var currentState = synchronizer.GetCurrentUnifiedState();
            Debug.Log($"Current Unified State: {currentState.ToDetailedString()}");
            
            // Force synchronization
            synchronizer.ForceSynchronization();
            Debug.Log("✅ Forced synchronization completed");
            
            // Get updated stats
            var stats = synchronizer.GetSynchronizationStats();
            Debug.Log($"Updated Stats: {stats}");
            
            Debug.Log("🎉 State synchronization test completed successfully!");
        }
        
        [Button("Remove Integration")]
        public void RemoveIntegration()
        {
            Debug.Log("=== REMOVING INTEGRATION ===");
            
            // Remove synchronizer
            var synchronizer = FindObjectOfType<CentralizedStateSynchronizer>();
            if (synchronizer != null)
            {
                DestroyImmediate(synchronizer.gameObject);
                Debug.Log("✅ Removed CentralizedStateSynchronizer");
            }
            
            // Re-enable old movement module if it exists
            var oldMovement = FindObjectOfType<RefactoredMovementModule>();
            if (oldMovement != null)
            {
                oldMovement.enabled = true;
                Debug.Log("✅ Re-enabled original MovementModule");
            }
            
            // Remove refactored movement module
            var refactoredMovement = FindObjectOfType<RefactoredMovementModule>();
            if (refactoredMovement != null)
            {
                DestroyImmediate(refactoredMovement);
                Debug.Log("✅ Removed RefactoredMovementModule");
            }
            
            // Remove integrator
            var integrator = FindObjectOfType<CentralizedStateSynchronizerIntegrator>();
            if (integrator != null)
            {
                DestroyImmediate(integrator.gameObject);
                Debug.Log("✅ Removed SystemIntegrator");
            }
            
            Debug.Log("🔄 Integration removed - system restored to original state");
        }
        
        [InfoBox("Integration Status", InfoMessageType.None)]
        [Button("Show Integration Guide")]
        public void ShowIntegrationGuide()
        {
            Debug.Log(@"
=== CENTRALIZED STATE SYNCHRONIZATION INTEGRATION GUIDE ===

QUICK START:
1. Click 'Quick Setup - Integrate Now!' button above
2. Click 'Verify Setup' to confirm integration
3. Click 'Test State Synchronization' to test functionality

MANUAL SETUP:
1. Add CentralizedStateSynchronizer component to player
2. Replace MovementModule with RefactoredMovementModule  
3. Configure synchronization settings
4. Test in Play mode

FEATURES:
✅ Unified state management across all animation modules
✅ Event-driven architecture for loose coupling
✅ Real-time state validation and error detection
✅ Performance monitoring and statistics
✅ Comprehensive debugging and logging

DOCUMENTATION:
- Full integration guide: Assets/Documentation/Integration/
- Component documentation: Assets/Scripts/Animation/
- Test suite: Assets/Tests/Editor/

SUPPORT:
- Use 'Verify Setup' for component status
- Use 'Test State Synchronization' for functionality testing
- Check console logs for detailed information
- Review integration guide for troubleshooting

=== READY TO INTEGRATE! ===
");
        }
    }
}
