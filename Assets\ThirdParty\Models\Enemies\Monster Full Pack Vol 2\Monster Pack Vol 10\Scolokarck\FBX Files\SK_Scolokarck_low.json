{"SK_Scolokarck_low": {"Version": "1.10.1822.1", "Scene": {"Name": true, "SupportShaderSelect": true}, "Export Directory": "D:/UnityProject/PersianArts/HybridCasualTempleClean/Assets/ThirdParty/Models/Enemies/Monster Full Pack Vol 2/Monster Pack Vol 10/Scolokarck/FBX Files", "Object": {"SK_Scolokarck_low": {"Generation": "", "Physics": {"Collision Shapes": {"RL_BoneRoot": {}, "SK_Scolokarck": {}, "root": {}, "Scolokarck_Hips": {}, "Scolokarck_LegRearLeft1": {}, "Scolokarck_LegRearLeft2": {}, "Scolokarck_LegRearLeft3": {}, "Scolokarck_LegRearLeft4": {}, "Scolokarck_LegRearRight1": {}, "Scolokarck_LegRearRight2": {}, "Scolokarck_LegRearRight3": {}, "Scolokarck_LegRearRight4": {}, "Scolokarck_Spine1": {}, "Scolokarck_LegFrontLeft1": {}, "Scolokarck_LegFrontLeft2": {}, "Scolokarck_LegFrontLeft3": {}, "Scolokarck_LegFrontLeft4": {}, "Scolokarck_LegFrontRight1": {}, "Scolokarck_LegFrontRight2": {}, "Scolokarck_LegFrontRight3": {}, "Scolokarck_LegFrontRight4": {}, "Scolokarck_Spine2": {}, "Scolokarck_ClawLowerLeft1": {}, "Scolokarck_ClawLowerLeft2": {}, "Scolokarck_ClawLowerLeft3": {}, "Scolokarck_ClawLowerRight1": {}, "Scolokarck_ClawLowerRight2": {}, "Scolokarck_ClawLowerRight3": {}, "Scolokarck_Spine3": {}, "Scolokarck_ClawMiddleLeft1": {}, "Scolokarck_ClawMiddleLeft2": {}, "Scolokarck_ClawMiddleLeft3": {}, "Scolokarck_ClawMiddleRight1": {}, "Scolokarck_ClawMiddleRight2": {}, "Scolokarck_ClawMiddleRight3": {}, "Scolokarck_Head": {}, "Scolokarck_ClawUpperLeft1": {}, "Scolokarck_ClawUpperLeft2": {}, "Scolokarck_ClawUpperLeft3": {}, "Scolokarck_ClawUpperRight1": {}, "Scolokarck_ClawUpperRight2": {}, "Scolokarck_ClawUpperRight3": {}, "Scolokarck_MandibleLowerLeft": {}, "Scolokarck_MandibleLowerRight": {}, "Scolokarck_MandibleUpperLeft": {}, "Scolokarck_MandibleUpperRight": {}, "Scolokarck_Tail1": {}, "Scolokarck_Tail2": {}, "Scolokarck_Tail3": {}, "Scolokarck_Tail4": {}, "SK_Scolokarck_1": {}}}, "Meshes": {"SK_Scolokarck_1": {"Materials": {"SK_Scolokarck_1_Merged0_LOD0_Bake": {"Material Type": "Pbr", "MultiUV Index": 0, "Two Side": true, "Diffuse Color": [255.0, 255.0, 255.0], "Ambient Color": [0.0, 0.0, 0.0], "Specular Color": [127.5, 127.5, 127.5], "Opacity": 1.0, "Self Illumination": 1.0, "Textures": {"Metallic": {"Texture Path": "", "Strength": 100.0, "Offset": [0.0, 0.0], "Tiling": [1.0, 1.0]}, "Base Color": {"Texture Path": "./SK_Scolokarck_low.fbm/SK_Scolokarck_1_Merged0_LOD0_Bake_Diffuse.png", "Strength": 100.0, "Offset": [0.0, 0.0], "Tiling": [1.0, 1.0]}, "Roughness": {"Texture Path": "", "Strength": 100.0, "Offset": [0.0, 0.0], "Tiling": [1.0, 1.0]}, "Opacity": {"Texture Path": "./SK_Scolokarck_low.fbm/SK_Scolokarck_1_Merged0_LOD0_Bake_Diffuse.png", "Strength": 100.0, "Offset": [0.0, 0.0], "Tiling": [1.0, 1.0]}, "Normal": {"Texture Path": "./SK_Scolokarck_low.fbm/SK_Scolokarck_1_Merged0_LOD0_Bake_Normal.png", "Strength": 100.0, "Offset": [0.0, 0.0], "Tiling": [1.0, 1.0]}, "MetallicAlpha": {"Texture Path": "./textures/SK_Scolokarck_low/SK_Scolokarck_low/SK_Scolokarck_1/SK_Scolokarck_1_Merged0_LOD0_Bake/SK_Scolokarck_1_Merged0_LOD0_Bake_MetallicAlpha.png"}, "HDRP": {"Texture Path": "./textures/SK_Scolokarck_low/SK_Scolokarck_low/SK_Scolokarck_1/SK_Scolokarck_1_Merged0_LOD0_Bake/SK_Scolokarck_1_Merged0_LOD0_Bake_MetallicAlpha.png"}}, "Resource Textures": {}}}}}}}}}