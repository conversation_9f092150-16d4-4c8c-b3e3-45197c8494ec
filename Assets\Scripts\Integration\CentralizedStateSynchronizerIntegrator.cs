using UnityEngine;
using Sirenix.OdinInspector;
using Animation.StateSync;
using DefaultNamespace.Mono.Interface;
using Module.Mono.Animancer.RealsticFemale;

namespace Integration
{
    /// <summary>
    /// Integration helper for setting up the Centralized State Synchronization system
    /// in existing game scenes and player prefabs.
    /// </summary>
    public class CentralizedStateSynchronizerIntegrator : MonoBehaviour
    {
        [Header("Integration Configuration")]
        [SerializeField] private bool autoIntegrateOnStart = false;
        [SerializeField] private bool enableDebugLogging = true;
        
        [Header("Target Components")]
        [SerializeField] private PlayerController playerController;
        [SerializeField] private CharacterParameters characterParameters;
        [SerializeField] private GameObject playerGameObject;
        
        [Header("Existing Modules (Auto-detected)")]
        [SerializeField, ReadOnly] private RefactoredMovementModule existingMovementModule;
        [SerializeField, ReadOnly] private RefactoredMovementModule refactoredMovementModule;
        [Serial<PERSON><PERSON><PERSON>, ReadOnly] private AimingModule aimingModule;
        //[SerializeField, ReadOnly] private WeaponModule weaponModule;
        [SerializeField, ReadOnly] private CharacterAnimationModule animationModule;
        
        [Header("State Synchronizer")]
        [SerializeField, ReadOnly] private CentralizedStateSynchronizer stateSynchronizer;
        
        [Header("Integration Status")]
        [SerializeField, ReadOnly] private bool isIntegrated = false;
        [SerializeField, ReadOnly] private string integrationStatus = "Not Started";
        
        private void Start()
        {
            if (autoIntegrateOnStart)
            {
                IntegrateSystem();
            }
        }
        
        [Button("Auto-Detect Components")]
        public void AutoDetectComponents()
        {
            // Auto-detect player controller
            if (playerController == null)
                playerController = FindObjectOfType<PlayerController>();
                
            // Auto-detect character parameters
            if (characterParameters == null)
                characterParameters = FindObjectOfType<CharacterParameters>();
                
            // Set player GameObject
            if (playerGameObject == null && playerController != null)
                playerGameObject = playerController.gameObject;
                
            // Auto-detect existing modules
            if (playerGameObject != null)
            {
                existingMovementModule = playerGameObject.GetComponentInChildren<RefactoredMovementModule>();
                refactoredMovementModule = playerGameObject.GetComponentInChildren<RefactoredMovementModule>();
                aimingModule = playerGameObject.GetComponentInChildren<AimingModule>();
                //weaponModule = playerGameObject.GetComponentInChildren<WeaponModule>();
                animationModule = playerGameObject.GetComponentInChildren<CharacterAnimationModule>();
                stateSynchronizer = playerGameObject.GetComponentInChildren<CentralizedStateSynchronizer>();
            }
            
            UpdateIntegrationStatus();
            Debug.Log("[Integrator] Component auto-detection completed.");
        }
        
        [Button("Integrate Centralized State Synchronization")]
        public void IntegrateSystem()
        {
            integrationStatus = "Starting Integration...";
            
            try
            {
                // Step 1: Auto-detect components if not set
                if (playerController == null || characterParameters == null)
                {
                    AutoDetectComponents();
                }
                
                // Step 2: Validate prerequisites
                if (!ValidatePrerequisites())
                {
                    integrationStatus = "Integration Failed - Missing Prerequisites";
                    return;
                }
                
                // Step 3: Add CentralizedStateSynchronizer if not present
                if (stateSynchronizer == null)
                {
                    AddCentralizedStateSynchronizer();
                }
                
                // Step 4: Configure the synchronizer
                ConfigureStateSynchronizer();
                
                // Step 5: Replace old MovementModule with RefactoredMovementModule
                ReplaceMovementModule();
                
                // Step 6: Update module references
                UpdateModuleReferences();
                
                // Step 7: Verify integration
                VerifyIntegration();
                
                isIntegrated = true;
                integrationStatus = "Integration Completed Successfully";
                
                Debug.Log("[Integrator] Centralized State Synchronization system integrated successfully!");
            }
            catch (System.Exception ex)
            {
                integrationStatus = $"Integration Failed: {ex.Message}";
                Debug.LogError($"[Integrator] Integration failed: {ex.Message}");
            }
        }
        
        [Button("Remove Integration")]
        public void RemoveIntegration()
        {
            if (stateSynchronizer != null)
            {
                DestroyImmediate(stateSynchronizer);
                stateSynchronizer = null;
            }
            
            isIntegrated = false;
            integrationStatus = "Integration Removed";
            
            Debug.Log("[Integrator] Centralized State Synchronization integration removed.");
        }
        
        private bool ValidatePrerequisites()
        {
            if (playerController == null)
            {
                Debug.LogError("[Integrator] PlayerController not found!");
                return false;
            }
            
            if (characterParameters == null)
            {
                Debug.LogError("[Integrator] CharacterParameters not found!");
                return false;
            }
            
            if (playerGameObject == null)
            {
                Debug.LogError("[Integrator] Player GameObject not set!");
                return false;
            }
            
            return true;
        }
        
        private void AddCentralizedStateSynchronizer()
        {
            // Create a new GameObject for the synchronizer
            GameObject syncObject = new GameObject("CentralizedStateSynchronizer");
            syncObject.transform.SetParent(playerGameObject.transform);
            
            // Add the synchronizer component
            stateSynchronizer = syncObject.AddComponent<CentralizedStateSynchronizer>();
            
            Debug.Log("[Integrator] CentralizedStateSynchronizer added to player.");
        }
        
        private void ConfigureStateSynchronizer()
        {
            if (stateSynchronizer == null) return;
            
            // Configure synchronizer settings
            stateSynchronizer.SetSynchronizationEnabled(true);
            stateSynchronizer.SetSynchronizationInterval(0.016f); // 60 FPS
            
            Debug.Log("[Integrator] CentralizedStateSynchronizer configured.");
        }
        
        private void ReplaceMovementModule()
        {
            // If old MovementModule exists and RefactoredMovementModule doesn't
            if (existingMovementModule != null && refactoredMovementModule == null)
            {
                GameObject movementObject = existingMovementModule.gameObject;
                
                // Disable old movement module
                existingMovementModule.enabled = false;
                
                // Add refactored movement module
                refactoredMovementModule = movementObject.AddComponent<RefactoredMovementModule>();
                
                // Copy configuration if possible
                CopyMovementConfiguration();
                
                Debug.Log("[Integrator] MovementModule replaced with RefactoredMovementModule.");
            }
        }
        
        private void CopyMovementConfiguration()
        {
            // This method would copy configuration from old to new movement module
            // Implementation depends on specific configuration structure
            Debug.Log("[Integrator] Movement configuration copied (if applicable).");
        }
        
        private void UpdateModuleReferences()
        {
            // Update PlayerController module references if needed
            if (playerController != null && refactoredMovementModule != null)
            {
                // This would update the module list in PlayerController
                // Implementation depends on PlayerController structure
                Debug.Log("[Integrator] Module references updated.");
            }
        }
        
        private void VerifyIntegration()
        {
            bool isValid = true;
            
            if (stateSynchronizer == null)
            {
                Debug.LogWarning("[Integrator] CentralizedStateSynchronizer not found after integration!");
                isValid = false;
            }
            
            if (refactoredMovementModule == null)
            {
                Debug.LogWarning("[Integrator] RefactoredMovementModule not found after integration!");
                isValid = false;
            }
            
            if (!isValid)
            {
                throw new System.Exception("Integration verification failed!");
            }
        }
        
        private void UpdateIntegrationStatus()
        {
            if (stateSynchronizer != null && refactoredMovementModule != null)
            {
                isIntegrated = true;
                integrationStatus = "Already Integrated";
            }
            else if (stateSynchronizer != null)
            {
                isIntegrated = false;
                integrationStatus = "Partially Integrated";
            }
            else
            {
                isIntegrated = false;
                integrationStatus = "Not Integrated";
            }
        }
        
        [Button("Test State Synchronization")]
        public void TestStateSynchronization()
        {
            if (stateSynchronizer == null)
            {
                Debug.LogWarning("[Integrator] CentralizedStateSynchronizer not found! Please integrate first.");
                return;
            }
            
            // Get current state
            var currentState = stateSynchronizer.GetCurrentUnifiedState();
            Debug.Log($"[Integrator] Current Unified State: {currentState.ToDetailedString()}");
            
            // Get statistics
            var stats = stateSynchronizer.GetSynchronizationStats();
            Debug.Log($"[Integrator] Synchronization Stats: {stats}");
            
            // Force synchronization
            stateSynchronizer.ForceSynchronization();
            Debug.Log("[Integrator] Forced state synchronization completed.");
        }
        
        [Button("Generate Integration Report")]
        public void GenerateIntegrationReport()
        {
            var report = $@"
=== CENTRALIZED STATE SYNCHRONIZATION INTEGRATION REPORT ===

Integration Status: {integrationStatus}
Is Integrated: {isIntegrated}

DETECTED COMPONENTS:
- PlayerController: {(playerController != null ? "✓" : "✗")}
- CharacterParameters: {(characterParameters != null ? "✓" : "✗")}
- Player GameObject: {(playerGameObject != null ? "✓" : "✗")}

EXISTING MODULES:
- MovementModule (Old): {(existingMovementModule != null ? "✓" : "✗")}
- RefactoredMovementModule: {(refactoredMovementModule != null ? "✓" : "✗")}
- AimingModule: {(aimingModule != null ? "✓" : "✗")}
- AnimationModule: {(animationModule != null ? "✓" : "✗")}

STATE SYNCHRONIZATION:
- CentralizedStateSynchronizer: {(stateSynchronizer != null ? "✓" : "✗")}

RECOMMENDATIONS:
{GetRecommendations()}
";
            
            Debug.Log(report);
        }
        
        private string GetRecommendations()
        {
            var recommendations = "";
            
            if (!isIntegrated)
            {
                recommendations += "- Run 'Integrate Centralized State Synchronization' to set up the system\n";
            }
            
            if (existingMovementModule != null && existingMovementModule.enabled)
            {
                recommendations += "- Disable old MovementModule to avoid conflicts\n";
            }
            
            if (stateSynchronizer == null)
            {
                recommendations += "- Add CentralizedStateSynchronizer component\n";
            }
            
            if (refactoredMovementModule == null)
            {
                recommendations += "- Add RefactoredMovementModule component\n";
            }
            
            if (string.IsNullOrEmpty(recommendations))
            {
                recommendations = "- System is properly integrated and ready to use!";
            }
            
            return recommendations;
        }
    }
}
