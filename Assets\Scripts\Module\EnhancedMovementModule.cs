using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using DefaultNamespace.Mono.Interface;
using DG.Tweening;
using Events;
using Mono.Extensions;
using Unity.Mathematics;
using UnityEngine;
using Controllers;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Enhanced MovementModule that combines:
    /// - Event-driven architecture from RefactoredMovementModule
    /// - CentralizedRotationManager integration
    /// - EdgeCaseControllerManager integration
    /// - Stuck state recovery mechanisms
    /// - Clean separation of concerns
    /// </summary>
    public class EnhancedMovementModule : MonoBehaviour, IModule<MovementSubState>, IUpdateSubModule<MovementSubState>,
        IUpdateState<AnimationSubState>
    {
        #region Properties and Fields

        public bool CanUpdate { get; private set; }

        [Header("Movement Configuration")]
        [Tooltip("Enable improved movement for smoother direction changes")]
        [SerializeField] private bool improvedMovement = true;
        [Tooltip("Use event-driven animation communication")]
        [SerializeField] private bool useEventDrivenAnimation = true;
        [Tooltip("Use centralized rotation management")]
        [SerializeField] private bool useCentralizedRotation = true;

        [Header("Movement Parameters")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float rotationSpeed = 180f;
        [SerializeField] private float aimingSpeedMultiplier = 0.6f;
        [SerializeField] private float strafingSpeedMultiplier = 0.8f;
        [SerializeField] private float backpedalSpeedMultiplier = 0.7f;

        [Header("Rotation Settings")]
        [SerializeField] private bool m_useWholeBodyRotation = true;
        [SerializeField] private float m_minRotationAngle = 30f;
        [SerializeField] private float m_targetRotationSpeed = 5f;
        [SerializeField] private float m_rotationSmoothTime = 0.2f;

        [Header("State Recovery")]
        [SerializeField] private bool enableStateRecovery = true;
        [SerializeField] private float stateRecoveryTimeout = 2f;

        [Header("Debug Settings")]
        [SerializeField] private bool enableDebugLogging = false;

        // Movement state
        private Vector3 currentVelocity = Vector3.zero;
        private Vector3 lastInputDirection = Vector3.zero;
        private float currentSpeed = 0f;
        private bool isMoving = false;

        // Rotation state
        private float lastInputAngle = 0f;
        private bool m_changeDirection = false;
        private bool m_waitForChangeDirection = false;
        private Tweener rotationTweener;
        private float m_currentRotationVelocity;

        // Component references
        private Transform m_playerTransform;
        private CharacterParameters characterParameters;
        private CentralizedRotationManager centralizedRotation;
        private EdgeCaseControllerManager controllerManager;

        // Module state
        [field: SerializeField] public int ControllerIndex { get; private set; }
        [field: SerializeField] public List<MainState> MainState { get; private set; }
        [SerializeField] private MovementSubState _moduleSubState;
        [SerializeField] private AnimationSubState _subState;

        // Task management
        private string _stateName;
        private CancellationTokenSource _cancellationTokenSource;
        private bool _isTransitioning = false;

        // State recovery tracking
        private float lastStateChangeTime;
        private float lastDirectionChangeRequestTime;
        private MovementSubState lastKnownGoodState = MovementSubState.Standing;

        // Input tracking
        [SerializeField] private float m_horizontal;
        [SerializeField] private float m_vertical;
        [SerializeField] private float m_inputAngle;
        [SerializeField] private float m_inputMagnitude;
        [SerializeField] private bool m_isAiming;
        [SerializeField] private Vector3 m_AimDirection;

        #endregion

        #region Properties

        public MovementSubState SubState
        {
            get { return _moduleSubState; }
            set
            {
                var previousState = _moduleSubState;
                _moduleSubState = value;

                // Track state changes for recovery
                if (previousState != value)
                {
                    lastStateChangeTime = Time.time;
                    if (value != MovementSubState.Stop)
                    {
                        lastKnownGoodState = value;
                    }
                }

                // Update StateManager
                PlayerController.Instance.StateManager.CurrentSubState = _moduleSubState;

                // Broadcast movement state change event (from RefactoredMovementModule)
                if (useEventDrivenAnimation)
                {
                    BroadcastMovementStateChange(previousState, value);
                }
            }
        }

        public bool IsMoving
        {
            get { return isMoving; }
            private set
            {
                if (isMoving != value)
                {
                    isMoving = value;
                    LogDebug($"Movement state changed: IsMoving = {isMoving}");
                }
            }
        }

        AnimationSubState IUpdateState<AnimationSubState>.SubState
        {
            get { return _subState; }
            set { _subState = value; }
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            // Initialize component references
            if (m_playerTransform == null)
                m_playerTransform = transform;

            // Get controller references
            centralizedRotation = GetComponent<CentralizedRotationManager>();
            controllerManager = GetComponent<EdgeCaseControllerManager>();

            // Initialize state
            CanUpdate = true;
            _moduleSubState = MovementSubState.Standing;

            LogDebug("EnhancedMovementModule initialized");
        }

        private void Start()
        {
            // Subscribe to events
            SubscribeToEvents();

            // Initialize with standing state
            if (useEventDrivenAnimation)
            {
                BroadcastMovementStateChange(MovementSubState.Standing, MovementSubState.Standing);
            }
        }

        private void OnDestroy()
        {
            // Cleanup
            UnsubscribeFromEvents();
            CancelAllTasks();

            if (rotationTweener != null && rotationTweener.IsActive())
                rotationTweener.Kill();
        }

        #endregion

        #region Event Management (from RefactoredMovementModule)

        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Subscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);

            LogDebug("Subscribed to movement events");
        }

        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Unsubscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);

            LogDebug("Unsubscribed from movement events");
        }

        /// <summary>
        /// Broadcast movement state change event (from RefactoredMovementModule)
        /// </summary>
        private void BroadcastMovementStateChange(MovementSubState previousState, MovementSubState newState)
        {
            if (previousState == newState && previousState != MovementSubState.Standing)
                return;

            var movementEvent = new OnMovementStateChangedEvent(
                previousState,
                newState,
                m_inputMagnitude,
                GetCurrentInputDirection(),
                IsMoving
            );

            EventManager.Broadcast(movementEvent);
            LogDebug($"Broadcasted movement state change: {previousState} → {newState}");
        }

        private Vector3 GetCurrentInputDirection()
        {
            return new Vector3(m_horizontal, 0, m_vertical).normalized;
        }

        #endregion

        #region Core Movement Logic (Enhanced)

        public void Initialize(int controllerIndex)
        {
            ControllerIndex = controllerIndex;
            lastInputAngle = characterParameters?.InputAngle.Value ?? 0f;

            // Broadcast initial state
            if (useEventDrivenAnimation)
            {
                BroadcastMovementStateChange(MovementSubState.Standing, MovementSubState.Standing);
            }

            LogDebug($"EnhancedMovementModule initialized with controller index: {controllerIndex}");
        }

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if (!CanProcessInput())
                return;

            if (characterParameters == null) return;

            SubState = (MovementSubState)currentSubState;

            // State recovery mechanism (from original MovementModule)
            if (enableStateRecovery)
            {
                CheckAndRecoverFromStuckStates();
            }

            // Get current input values
            UpdateInputValues();

            // Handle direction changes
            if (m_changeDirection)
                return;

            // Apply whole body rotation with centralized rotation management
            if (characterParameters.IsAiming && m_useWholeBodyRotation)
            {
                UpdateCharacterRotation();
            }

            // Apply movement with enhanced controller integration
            ApplyMovement(m_horizontal, m_vertical);

            // Update current substate reference
            currentSubState = SubState;
        }

        private void UpdateInputValues()
        {
            m_horizontal = characterParameters.Horizontal.Value;
            m_vertical = characterParameters.Vertical.Value;
            m_inputAngle = characterParameters.InputAngle.Value;
            m_inputMagnitude = characterParameters.InputMagnitude.Value;
            m_isAiming = characterParameters.IsAiming;
            m_AimDirection = characterParameters.AimTarget;
        }

        #endregion

        #region Enhanced Rotation Management

        /// <summary>
        /// Enhanced character rotation with centralized rotation management
        /// </summary>
        private void UpdateCharacterRotation()
        {
            if (characterParameters == null || m_playerTransform == null) return;

            float3 targetPosition = characterParameters.AimTarget;
            if (targetPosition.Equals(float3.zero)) return;

            // Use CentralizedRotationManager if available
            if (useCentralizedRotation && centralizedRotation != null)
            {
                bool rotationRequested = centralizedRotation.RequestRotation(
                    CentralizedRotationManager.RotationPriority.Aiming,
                    targetPosition,
                    m_targetRotationSpeed,
                    true,
                    "EnhancedMovementModule"
                );

                if (rotationRequested)
                {
                    LogDebug("Rotation requested through CentralizedRotationManager");
                    return;
                }
            }

            // Fallback to original rotation logic
            ApplyFallbackRotation(targetPosition);
        }

        private void ApplyFallbackRotation(float3 targetPosition)
        {
            Vector3 directionToTarget = (Vector3)targetPosition - m_playerTransform.position;
            directionToTarget.y = 0;

            if (directionToTarget.magnitude < 0.1f) return;

            float targetAngle = Mathf.Atan2(directionToTarget.x, directionToTarget.z) * Mathf.Rad2Deg;
            float currentAngle = m_playerTransform.eulerAngles.y;
            float angleDifference = Mathf.DeltaAngle(currentAngle, targetAngle);

            if (Mathf.Abs(angleDifference) > m_minRotationAngle)
            {
                float newAngle = Mathf.SmoothDampAngle(
                    currentAngle,
                    targetAngle,
                    ref m_currentRotationVelocity,
                    m_rotationSmoothTime);

                m_playerTransform.rotation = Quaternion.Euler(0, newAngle, 0);
                LogDebug($"Fallback rotation applied. Angle diff: {angleDifference:F1}°");
            }
        }

        #endregion

        #region Enhanced Movement Application

        /// <summary>
        /// Enhanced movement application with controller integration
        /// </summary>
        private void ApplyMovement(float horizontalInput, float verticalInput)
        {
            // Get processed input from EdgeCaseControllerManager
            Vector2 rawInput = new Vector2(horizontalInput, verticalInput);
            Vector2 processedInput = rawInput;

            if (controllerManager != null && !m_changeDirection && !m_waitForChangeDirection)
            {
                processedInput = controllerManager.GetProcessedInput(rawInput);
            }

            horizontalInput = processedInput.x;
            verticalInput = processedInput.y;

            Vector3 inputDirection = new Vector3(horizontalInput, 0, verticalInput);
            float inputMagnitude = inputDirection.magnitude;

            if (inputMagnitude > 1f)
                inputDirection.Normalize();
            else if (inputMagnitude < 0.01f)
                inputDirection = Vector3.zero;

            // Get calculated speed from EdgeCaseControllerManager
            float targetSpeed = controllerManager?.GetCalculatedSpeed(moveSpeed) ?? moveSpeed;

            bool isAiming = characterParameters.IsAiming;

            // Apply speed modifiers if EdgeCaseControllerManager not available
            if (controllerManager == null)
            {
                if (isAiming)
                    targetSpeed *= aimingSpeedMultiplier;

                if (isAiming && Mathf.Abs(horizontalInput) > 0.7f)
                    targetSpeed *= strafingSpeedMultiplier;

                if (isAiming && verticalInput < -0.3f)
                    targetSpeed *= backpedalSpeedMultiplier;
            }

            float currentSpeed = targetSpeed * inputMagnitude;

            // Apply movement
            if (inputDirection.magnitude > 0)
            {
                Vector3 movementVector = inputDirection * currentSpeed * Time.deltaTime;
                m_playerTransform.position += movementVector;

                // Apply rotation with centralized rotation integration
                ApplyMovementRotation(inputDirection, isAiming, inputMagnitude);
            }

            // Update movement state
            IsMoving = inputMagnitude > 0.1f;

            // Store last direction for next frame
            if (inputDirection.magnitude > 0.1f)
            {
                lastInputDirection = inputDirection;
            }
        }

        private void ApplyMovementRotation(Vector3 inputDirection, bool isAiming, float inputMagnitude)
        {
            // Only apply rotation if centralized rotation is not active
            if (useCentralizedRotation && centralizedRotation != null && centralizedRotation.IsCentralizedRotationActive())
            {
                return;
            }

            // Only apply normal rotation if not in fast rotation and not aiming
            if ((rotationTweener == null || !rotationTweener.IsActive()) && !isAiming)
            {
                float adaptiveRotationSpeed = rotationSpeed * Mathf.Lerp(0.5f, 1.0f, inputMagnitude);
                Quaternion targetRotation = Quaternion.LookRotation(inputDirection);
                m_playerTransform.rotation = Quaternion.RotateTowards(
                    m_playerTransform.rotation,
                    targetRotation,
                    adaptiveRotationSpeed * Time.deltaTime
                );
            }
        }

        #endregion

        #region State Recovery (from original MovementModule)

        private void CheckAndRecoverFromStuckStates()
        {
            float inputMagnitude = characterParameters.InputMagnitude.Value;
            bool isAiming = characterParameters.IsAiming;

            // Check if stuck in stop state while trying to move
            if (SubState == MovementSubState.Stop && inputMagnitude > 0.2f && !characterParameters.IsStopping)
            {
                if (Time.time - lastStateChangeTime > stateRecoveryTimeout)
                {
                    LogDebug("Detected stuck in Stop state, recovering...");
                    
                    SubState = MovementSubState.Standing;
                    IsMoving = false;
                    m_waitForChangeDirection = false;
                    m_changeDirection = false;
                    characterParameters.IsStopping = false;
                    
                    CancelPreviousTask("StuckStateRecovery");
                    LogDebug("Recovered from stuck Stop state");
                }
            }

            // Check for stuck direction change
            if (m_waitForChangeDirection && !m_changeDirection && inputMagnitude > 0.2f)
            {
                if (lastDirectionChangeRequestTime == 0)
                {
                    lastDirectionChangeRequestTime = Time.time;
                }

                if (Time.time - lastDirectionChangeRequestTime > 1.5f)
                {
                    LogDebug("Direction change timeout, recovering...");
                    
                    m_waitForChangeDirection = false;
                    m_changeDirection = false;
                    lastDirectionChangeRequestTime = 0;
                    
                    if (inputMagnitude > 0.2f)
                    {
                        SubState = MovementSubState.Standing;
                        IsMoving = false;
                    }
                    
                    LogDebug("Recovered from stuck direction change");
                }
            }
            else if (!m_waitForChangeDirection)
            {
                lastDirectionChangeRequestTime = 0;
            }
        }

        #endregion

        #region Event Handlers (Enhanced)

        private void OnChangeDirection(OnChangeDirectionEvent onChangeDirectionEvent)
        {
            float inputMagnitude = characterParameters.InputMagnitude.Value;
            bool isAiming = characterParameters.IsAiming;

            // Skip direction change if in problematic state combination
            if (isAiming && inputMagnitude < 0.1f && SubState == MovementSubState.Stop)
            {
                LogDebug("Skipping direction change - aiming while stopped");
                return;
            }

            if (_isTransitioning || m_changeDirection)
            {
                LogDebug("Skipping direction change - already in transition");
                return;
            }

            lastDirectionChangeRequestTime = Time.time;

            if (!m_waitForChangeDirection && !characterParameters.WantsToRotateBehindTarget &&
                (characterParameters.CurrentPlayerSpeed.Value > 0.1f || onChangeDirectionEvent.IsAimToNormal))
            {
                m_waitForChangeDirection = true;
                LogDebug($"Direction change initiated: IsAimToNormal = {onChangeDirectionEvent.IsAimToNormal}");
            }
        }

        private void OnCharacterAnimationFinished(OnCharacterAnimationFinishedEvent eventData)
        {
            LogDebug($"Animation finished: {eventData.MovementSubState}");
            // Handle animation completion logic
        }

        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            LogDebug($"Target detected at: {eventData.TargetPosition}");
        }

        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            LogDebug("Target lost");
        }

        private void OnRequireFastRotation(OnRequireFastRotationEvent eventData)
        {
            LogDebug($"Fast rotation requested towards: {eventData.TargetPosition}");
            
            // Use centralized rotation for fast rotation if available
            if (useCentralizedRotation && centralizedRotation != null)
            {
                centralizedRotation.RequestRotation(
                    CentralizedRotationManager.RotationPriority.FastRotation,
                    eventData.TargetPosition,
                    rotationSpeed * 2f,
                    false,
                    "FastRotation"
                );
            }
            else
            {
                // Fallback to DOTween rotation
                PerformFastRotationFallback(eventData.TargetPosition);
            }
        }

        private void PerformFastRotationFallback(Vector3 targetPosition)
        {
            Vector3 direction = (targetPosition - m_playerTransform.position).normalized;
            direction.y = 0;

            if (direction != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(direction);

                if (rotationTweener != null && rotationTweener.IsActive())
                    rotationTweener.Kill();

                rotationTweener = m_playerTransform.DORotateQuaternion(targetRotation, 0.2f)
                    .SetEase(Ease.OutQuint);
            }
        }

        #endregion

        #region Utility Methods

        public bool CanProcessInput()
        {
            return !_isTransitioning && CanUpdate;
        }

        private void CancelPreviousTask(string taskName)
        {
            if (_cancellationTokenSource != null && !_cancellationTokenSource.IsCancellationRequested)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
            }
        }

        private void CancelAllTasks()
        {
            CancelPreviousTask("All");
        }

        public void SetState()
        {
            throw new NotImplementedException();
        }

        public void SetSubModule()
        {
            throw new NotImplementedException();
        }

        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[EnhancedMovementModule] {message}");
            }
        }

        #endregion
    }
}
