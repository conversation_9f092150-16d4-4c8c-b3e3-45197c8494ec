using Unity.Entities;
using PlayerFAP.Components.Weapon;
using PlayerFAP.Tags;
using PlayerFAP.ScriptableObjects;
using DefaultNamespace.Mono.Interface;
using Module.Mono.Animancer.RealsticFemale;
using Module.Weapon;
using UnityEngine;

namespace PlayerFAP.Systems.Weapon
{
    /// <summary>
    /// System to update weapon detection settings based on the current weapon
    /// </summary>
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    public partial class WeaponDetectionSettingsSystem : SystemBase
    {
        private EntityQuery _playerQuery;

        protected override void OnCreate()
        {
            _playerQuery = GetEntityQuery(
                ComponentType.ReadOnly<PlayerTag>(),
                ComponentType.ReadWrite<WeaponDetectionSettings>()
            );

            RequireForUpdate<PlayerTag>();
        }

        protected override void OnUpdate()
        {
            if(!GameManager.Instance)
                return;

            // Find the weapon module to get the current weapon level
            var weaponModule = (RefactoredWeaponModule)GameManager.Instance.moduleManager.GetModule<RefactoredWeaponModule>();
            if (weaponModule == null) return;

            int currentWeaponLevel = weaponModule.currentWeaponLevel;

            // Get the current weapon type from the player controller
            var playerController = PlayerController.Instance;
            if (playerController == null) return;

            var currentWeaponType = (WeaponSubModuleState)playerController.StateManager.GetState(typeof(WeaponSubModuleState));

            // Find the weapon config for the current weapon type
            WeaponConfig weaponConfig = null;
            var weaponConfigs = Resources.LoadAll<WeaponConfig>("Weapons");
            foreach (var config in weaponConfigs)
            {
                if (config.weaponType == currentWeaponType)
                {
                    weaponConfig = config;
                    break;
                }
            }

            if (weaponConfig == null)
            {
                //Debug.LogWarning($"No weapon config found for weapon type {currentWeaponType}");
                return;
            }

            // Update the detection settings for all player entities
            var weaponConfigCopy = weaponConfig;
            var currentLevelCopy = currentWeaponLevel;
            var currentTypeCopy = currentWeaponType;
            var entityManager = EntityManager;

            // First, make sure all player entities have the WeaponDetectionSettings component
            Entities
                .WithAll<PlayerTag>()
                .WithNone<WeaponDetectionSettings>()
                .ForEach((Entity entity) =>
                {
                    entityManager.AddComponent<WeaponDetectionSettings>(entity);
                    Debug.Log($"Added WeaponDetectionSettings to player entity {entity.Index}");
                })
                .WithStructuralChanges()
                .Run();

            // Then update the settings for all player entities
            Entities
                .WithAll<PlayerTag>()
                .ForEach((ref WeaponDetectionSettings settings) =>
                {
                    // Only update if the weapon type or level has changed
                    if (settings.WeaponType != currentTypeCopy || settings.WeaponLevel != currentLevelCopy)
                    {
                        settings.InitializeFromConfig(weaponConfigCopy, currentLevelCopy);
                        Debug.Log($"Updated WeaponDetectionSettings for weapon {currentTypeCopy} at level {currentLevelCopy}");
                    }
                })
                .WithoutBurst()
                .Run();
        }
    }
}
