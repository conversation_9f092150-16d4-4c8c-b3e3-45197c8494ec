%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Ga_Skin_Body
  m_Shader: {fileID: -6465566751694194690, guid: df5bb027d94a6c44bb32b3c31ec1303f,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - TCP2_REFLECTIONS_FRESNEL
  - TCP2_RIM_LIGHTING_LIGHTMASK
  - TCP2_SHADOW_LIGHT_COLOR
  - TCP2_UV_NORMALS_FULL
  m_InvalidKeywords:
  - _METALLICSPECGLOSSMAP
  - _OCCLUSIONMAP
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses: []
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 66592cc9f4b285b4293b28f2b3499bc8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 83ce05cf871595748987fd2b615b4e9e, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 20, y: 20}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 01ec98040a7b16c4ca668df180a1ceb6, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: 9372e61577b63764fb7697963c7d43ff, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowBaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 2800000, guid: 088d3a0e290606a4682cc5bd5eef282a, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 2800000, guid: c693eb0d4051a4641896ce2ae92c92d7, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AlphaCutoff: 0.5
    - _BumpScale: 1
    - _Cull: 2
    - _Cutoff: 0.5
    - _DetailNormalMapScale: 0.5
    - _DirectIntensityOutline: 1
    - _DstBlend: 0
    - _EmissionChannel: 4
    - _EnvironmentReflections: 1
    - _FresnelMax: 1.5
    - _FresnelMin: 0
    - _IndirectIntensity: 1
    - _IndirectIntensityOutline: 0
    - _MatCapMaskChannel: 0
    - _MatCapType: 0
    - _Metallic: 1
    - _NormalsSource: 0
    - _NormalsUVType: 0
    - _OcclusionChannel: 0
    - _OcclusionStrength: 1
    - _OutlineLightingType: 0
    - _OutlineLightingTypeURP: 0
    - _OutlineMaxWidth: 1
    - _OutlineMinWidth: 1
    - _OutlinePixelSizeType: 0
    - _OutlineTextureLOD: 5
    - _OutlineTextureType: 0
    - _OutlineWidth: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RampBands: 4
    - _RampBandsSmoothing: 0.1
    - _RampOffset: 0
    - _RampScale: 1
    - _RampSmoothing: 0.1
    - _RampThreshold: 0.75
    - _RampType: 0
    - _ReceiveShadows: 1
    - _ReceiveShadowsOff: 1
    - _ReflectionMapType: 0
    - _ReflectionSmoothness: 0.5
    - _RenderingMode: 0
    - _RimMax: 1
    - _RimMin: 0.5
    - _ShadowColorLightAtten: 1
    - _SingleIndirectColor: 0
    - _Smoothness: 0.625
    - _SpecularHighlights: 1
    - _SpecularMapType: 0
    - _SpecularRoughness: 0.5
    - _SpecularToonSize: 0.25
    - _SpecularToonSmoothness: 0.05
    - _SpecularType: 0
    - _SrcBlend: 1
    - _SubsurfaceMask: 0.34
    - _Thickness: 0.1
    - _TransAmbient: 0.1
    - _TransDirect: 0.9
    - _TransNormal: 0.95
    - _TransScattering: 2
    - _TransShadow: 0.5
    - _TransStrength: 1.5
    - _TransmissionShadow: 0.5
    - _UseAlphaTest: 0
    - _UseEmission: 0
    - _UseFresnelReflections: 1
    - _UseMatCap: 0
    - _UseMatCapMask: 0
    - _UseMobileMode: 0
    - _UseNormalMap: 0
    - _UseOcclusion: 0
    - _UseOutline: 0
    - _UseReflections: 0
    - _UseRim: 0
    - _UseRimLightMask: 1
    - _UseShadowTexture: 0
    - _UseSpecular: 0
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.99999994, g: 0.99999994, b: 0.99999994, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 0}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
    - _SColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecularColor: {r: 0.75, g: 0.75, b: 0.75, a: 1}
    - _SubsurfaceFalloff: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
