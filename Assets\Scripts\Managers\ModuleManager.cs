﻿using System;
using System.Collections.Generic;
using System.Linq;
using DefaultNamespace.Mono.Interface;
using Module.Mono;
using Sirenix.OdinInspector;
using UnityEngine;

public class ModuleManager : SerializedMonoBehaviour
{
    [SerializeField] private Dictionary<string, IModule> _modules = new Dictionary<string, IModule>();

    [SerializeField] private PlayerControllerBase _playerController;

    private void Awake()
    {
            _modules = new Dictionary<string, IModule>();
    }

    public void Initialize(PlayerControllerBase playerController)
    {
        _playerController = playerController;
    }

    public void AddModule(string moduleName, IModule module)
    {
        if (_modules.ContainsKey(moduleName))
            throw new Exception("Module already exists");

        _modules.Add(moduleName, module);
        //_playerController.AddModule(module);
    }

    public IModule GetModule<T>() where T : IModule
    {
        return _modules.ToList().Find(x => x.Value.GetType() == typeof(T)).Value;
    }

    public void RemoveModule(string moduleName)
    {
        if (!_modules.ContainsKey(moduleName))
            return;

        var module = _modules[moduleName];
        _playerController.RemoveModule(module);
        _modules.Remove(moduleName);
    }

    public void DisableModule<T>() where T : IModule
    {
        var module = _modules.ToList().Find(x => x.Value.GetType() == typeof(T));
        if (module.Value != null)
        {
            if (module.Value is MonoBehaviour monoBehaviour)
            {
                monoBehaviour.enabled = false;
            }
        }
    }

    public void EnableModule<T>() where T : IModule
    {
        var module = _modules.ToList().Find(x => x.Value.GetType() == typeof(T));
        if (module.Value != null)
        {
            if (module.Value is MonoBehaviour monoBehaviour)
            {
                monoBehaviour.enabled = true;
            }
        }
    }
}