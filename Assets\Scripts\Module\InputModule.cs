using System;
using System.Collections.Generic;
using System.Linq;
using DefaultNamespace.Mono.Interface;
using Events;
using MonoToECSShadow.Components;
using Rewired;
using Sirenix.OdinInspector;
using Unity.Entities;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    public class InputModule : SerializedMonoBehaviour, IInputModule, IHasOutputValue<CharacterParameterEnum>,
        INeedState<MovementSubState>
    {
        public bool CanUpdate { get; private set; }
        [SerializeField] private Transform m_playerTransform;
        [SerializeField] private float inputAngleMultiplier = 10;

        [field: SerializeField] public int ControllerIndex { get; private set; }
        [field: SerializeField] public List<MainState> MainState { get; private set; }
        [field: SerializeField] public PlaceHolder SubState { get; private set; }

        public GameObject CharacterPositionCompass;
        public GameObject InputDirectionCompass;

        [field: SerializeField]
        public Dictionary<CharacterParameterEnum, List<Action<object>>> OutputValue { get; private set; }

        [field: SerializeField] public bool HasRefreshRate { get; private set; }
        [field: SerializeField] public float RefreshRate { get; private set; }
        public Enum GetModuleSubState() => SubState;
        [field: SerializeField] public MovementSubState State { get; private set; }

        [SerializeField] private float rawHorizontalInput;
        [SerializeField] private float rawVerticalInput;

        [field: SerializeField] public float horizontalInput { get; private set; }
        [field: SerializeField] public float verticalInput { get; private set; }
        [field: SerializeField] public float startInputAngle { get; private set; }
        [field: SerializeField] public int inputAngle { get; private set; }
        [field: SerializeField] public float stopInputAngle { get; private set; }
        [field: SerializeField] public float inputMagnitude { get; private set; }
        [field: SerializeField] public float runFactor { get; private set; }

        [field: SerializeField] public Vector3 playerInputDirection { get; private set; }

        [Header("Movement Flags")] [SerializeField]
        private bool m_isRunning;

        [SerializeField] private bool m_isLeftMoving;
        [SerializeField] private bool m_isRightMoving;
        [SerializeField] private bool m_isFwrdMoving;
        [SerializeField] private bool m_isBackMoving;
        [SerializeField] private bool m_isLeftFootstep;
        [SerializeField] private bool m_isRightFootstep;

        [Header("Weapon Input")] [SerializeField]
        private int m_currentWeaponIndex;

        [SerializeField] private int m_maxWeaponIndex;
        [SerializeField] private bool m_equipWeapon;
        [SerializeField] private bool m_unEquipWeapon;

        [Header("Interactor")] [SerializeField]
        private bool m_interact;

        [Header("Aiming")] public bool EnableAutoAim;
        public bool IsAiming;
        public bool IsDetectTarget;

        [Header("Shooting")] public bool EnableAutoShooting;
        public bool IsShooting;
        public bool LastShootingState;

        [SerializeField] private List<INeedInput<CharacterParameterEnum>> _needInputComponents;

        private Rewired.Player _player; // the Rewired player

        private Rewired.Player player
        {
            get
            {
                // Get the Rewired Player object for this player. Refresh it as needed so it will get the new reference after a script recompile in the editor.
                if (_player == null) _player = ReInput.players.GetPlayer(0);
                return _player;
            }
        }

        private void Start()
        {
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingOnTarget);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnUnAimingTarget);
        }

        private void OnDestroy()
        {
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingOnTarget);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnUnAimingTarget);
        }

        public void Initialize(int controllerIndex)
        {
            EventManager.Subscribe<OnFootStepEvent>(OnFootStep);
            EventManager.Subscribe<OnDetectTargetEvent>(OnDetectTarget);
            EventManager.Subscribe<OnLostTargetEvent>(OnLostTarget);

            OutputValue = new Dictionary<CharacterParameterEnum, List<Action<object>>>();

            foreach (var inputComponent in _needInputComponents)
            {
                var inputValues = new Dictionary<CharacterParameterEnum, object>();

                foreach (var inputName in inputComponent.InputName)
                {
                    if (!OutputValue.ContainsKey(inputName))
                    {
                        OutputValue[inputName] = new List<Action<object>>();
                    }

                    // Capture the 'inputComponent' and 'inputName' in the lambda
                    OutputValue[inputName].Add(value =>
                    {
                        inputValues[inputName] = value;

                        // Check if all required input values are available
                        if (inputComponent.InputName.Any(name => inputValues.ContainsKey(name)))
                        {
                            // Call HandleInput with the complete inputValues dictionary
                            inputComponent.HandleInput(inputValues);
                        }
                    });
                }
            }

            DebugLogManager.Instance.Log("InputModule Initialized");
        }

        private void OnDetectTarget(OnDetectTargetEvent onDetectTargetEvent)
        {
            IsDetectTarget = true;
        }

        private void OnLostTarget(OnLostTargetEvent onLostTargetEvent)
        {
            IsDetectTarget = false;
        }

        private void OnAimingOnTarget(OnAimingOnTargetEvent onAimingOnTargetEvent)
        {
            if (EnableAutoAim)
                IsAiming = true;
        }

        private void OnUnAimingTarget(OnUnAimingTargetEvent onUnAimingTarget)
        {
            IsAiming = false;
        }

        private void OnFootStep(OnFootStepEvent onFootStepEvent)
        {
            m_isLeftFootstep = onFootStepEvent.IsLeftFoot;
            m_isRightFootstep = !onFootStepEvent.IsLeftFoot;
        }

        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if (!ReInput.isReady) return;

            // Fetch the raw input from the player
            rawHorizontalInput = player.GetAxis("Move Horizontal");
            rawVerticalInput = player.GetAxis("Move Vertical");

            // Get camera forward and right directions
            Vector3 cameraForward = Camera.main.transform.forward;
            Vector3 cameraRight = Camera.main.transform.right;
            cameraForward.y = 0; // Keep the direction horizontal
            cameraRight.y = 0; // Keep the direction horizontal
            cameraForward.Normalize();
            cameraRight.Normalize();

            // Determine input direction relative to camera
            Vector3 inputDirection = (rawHorizontalInput * cameraRight + rawVerticalInput * cameraForward).normalized;

            // Store previous input values for smoothing
            float prevHorizontal = horizontalInput;
            float prevVertical = verticalInput;

            if (IsAiming)
            {
                // Convert input direction to local space of the player for strafing
                Vector3 localInputDirection = m_playerTransform.InverseTransformDirection(inputDirection);

                // Apply a slight forward bias for more natural strafing movement
                // This makes diagonal movement feel more natural during aiming
                if (localInputDirection.magnitude > 0.1f)
                {
                    // Bias forward movement slightly for more natural strafing
                    float forwardBias = 0.15f; // Adjust this value to control the bias amount
                    if (localInputDirection.z > 0)
                    {
                        localInputDirection.z += forwardBias * (1 - localInputDirection.z);
                    }

                    // Normalize to ensure we don't exceed input bounds
                    if (localInputDirection.magnitude > 1f)
                    {
                        localInputDirection.Normalize();
                    }
                }

                // Set the input values
                horizontalInput = localInputDirection.x;
                verticalInput = localInputDirection.z;

                // Apply smoothing for aiming movement to prevent jerky transitions
                float aimingSmoothTime = 0.08f; // Lower value = faster response
                horizontalInput = Mathf.Lerp(prevHorizontal, horizontalInput, Time.deltaTime / aimingSmoothTime);
                verticalInput = Mathf.Lerp(prevVertical, verticalInput, Time.deltaTime / aimingSmoothTime);
            }
            else
            {
                // For normal movement, use the camera-relative input directly
                horizontalInput = inputDirection.x;
                verticalInput = inputDirection.z;

                // Apply less aggressive smoothing for normal movement
                float normalSmoothTime = 0.05f; // Lower value = faster response
                horizontalInput = Mathf.Lerp(prevHorizontal, horizontalInput, Time.deltaTime / normalSmoothTime);
                verticalInput = Mathf.Lerp(prevVertical, verticalInput, Time.deltaTime / normalSmoothTime);
            }

            // Continue with the original processing of inputs
            OutputValue[CharacterParameterEnum.Horizontal].ForEach(x => x.Invoke(horizontalInput));
            OutputValue[CharacterParameterEnum.Vertical].ForEach(x => x.Invoke(verticalInput));

            if (player.GetButtonDown("Interact"))
            {
                m_interact = true;
            }

            OutputValue[CharacterParameterEnum.Interact].ForEach(x => x.Invoke(m_interact));

            if (player.GetButtonDown("WeaponSwitchUp"))
            {
                m_currentWeaponIndex++;
                if (m_currentWeaponIndex > m_maxWeaponIndex)
                    m_currentWeaponIndex = 0;
            }

            if (player.GetButtonDown("WeaponSwitchDown"))
            {
                m_currentWeaponIndex--;
                if (m_currentWeaponIndex < 0)
                    m_currentWeaponIndex = m_maxWeaponIndex;
            }

            OutputValue[CharacterParameterEnum.WeaponIndex].ForEach(x => x.Invoke(m_currentWeaponIndex));

            if (verticalInput > 0)
            {
                m_isFwrdMoving = true;
                m_isBackMoving = false;
            }
            else if (verticalInput < 0)
            {
                m_isFwrdMoving = false;
                m_isBackMoving = true;
            }
            else
            {
                m_isFwrdMoving = false;
                m_isBackMoving = false;
            }

            if (horizontalInput > 0)
            {
                m_isLeftMoving = false;
                m_isRightMoving = true;
            }
            else if (horizontalInput < 0)
            {
                m_isLeftMoving = true;
                m_isRightMoving = false;
            }
            else
            {
                m_isLeftMoving = false;
                m_isRightMoving = false;
            }

            var inputVector = new Vector3(horizontalInput, 0, verticalInput);
            inputMagnitude = inputVector.magnitude;
            OutputValue[CharacterParameterEnum.InputMagnitude].ForEach(x => x.Invoke(inputMagnitude));

            OutputValue[CharacterParameterEnum.InputVector].ForEach(x => x.Invoke(inputVector));

            //inputAngle = Mathf.DeltaAngle(CharacterPositionCompass.transform.rotation.eulerAngles.magnitude, InputDirectionCompass.transform.rotation.eulerAngles.magnitude);
            inputAngle = (int) SignedAngle(CharacterPositionCompass.transform, InputDirectionCompass.transform);

            if (inputMagnitude > 0.2f && State == MovementSubState.Standing)
            {
                startInputAngle = inputAngle;
                OutputValue[CharacterParameterEnum.StartWalkAngle].ForEach(x => x.Invoke(startInputAngle));
            }

            OutputValue[CharacterParameterEnum.InputAngle].ForEach(x => x.Invoke(inputAngle));

            if (State == MovementSubState.Stop)
            {
                stopInputAngle = inputAngle;
                OutputValue[CharacterParameterEnum.StopWalkAngle].ForEach(x => x.Invoke(stopInputAngle));
            }

            // Update player input direction
            playerInputDirection = inputDirection;
            OutputValue[CharacterParameterEnum.PlayerInputDirection]
                .ForEach(x => x.Invoke(playerInputDirection.normalized));

            OutputValue[CharacterParameterEnum.AutoAiming].ForEach(x => x.Invoke(EnableAutoAim));
            OutputValue[CharacterParameterEnum.IsAiming].ForEach(x => x.Invoke(IsAiming));
            OutputValue[CharacterParameterEnum.DetectTarget].ForEach(x => x.Invoke(IsDetectTarget));

            // Get shoot button state for raw input - WeaponModule will handle the auto-shooting logic
            bool isShootButtonHeld = player.GetButton("Shoot"); // Changed from GetButtonDown to GetButton
            IsShooting = isShootButtonHeld;
            
            // Note: All auto-shooting logic and ECS updates have been moved to WeaponModule
            // WeaponModule will handle combining manual shooting with auto-shooting capability

            OutputValue[CharacterParameterEnum.AutoShooting].ForEach(x => x.Invoke(EnableAutoShooting));
            OutputValue[CharacterParameterEnum.IsShooting].ForEach(x => x.Invoke(IsShooting));

            runFactor = Mathf.Clamp(inputMagnitude, .5f, 1f);
            m_isRunning = runFactor > 0.5f;
            OutputValue[CharacterParameterEnum.RunningFactor].ForEach(x => x.Invoke(runFactor));

            OutputValue[CharacterParameterEnum.IsBackMoving].ForEach(x => x.Invoke(m_isBackMoving));
            OutputValue[CharacterParameterEnum.IsLeftMoving].ForEach(x => x.Invoke(m_isLeftMoving));
            OutputValue[CharacterParameterEnum.IsRightMoving].ForEach(x => x.Invoke(m_isRightMoving));
            OutputValue[CharacterParameterEnum.IsFwrdMoving].ForEach(x => x.Invoke(m_isFwrdMoving));
            OutputValue[CharacterParameterEnum.IsLeftFootstep].ForEach(x => x.Invoke(m_isLeftFootstep));
            OutputValue[CharacterParameterEnum.IsRightFootstep].ForEach(x => x.Invoke(m_isRightFootstep));
        }

        private float SignedAngle(Transform source, Transform target)
        {
            Vector3 forwardA = source.rotation * Vector3.forward;
            Vector3 forwardB = target.rotation * Vector3.forward;

            float angleA = Mathf.Atan2(forwardA.x, forwardA.z) * Mathf.Rad2Deg;
            float angleB = Mathf.Atan2(forwardB.x, forwardB.z) * Mathf.Rad2Deg;

            return Mathf.DeltaAngle(angleA, angleB);
        }


        public Type GetModuleState()
        {
            return State.GetType();
        }

        public void OnStateChange(MovementSubState newState)
        {
            State = newState;
        }

        private void OnDrawGizmosSelected()
        {
            // Draw debug lines
            Gizmos.color = Color.red;
            Gizmos.DrawLine(CharacterPositionCompass.transform.position,
                CharacterPositionCompass.transform.position + CharacterPositionCompass.transform.forward * 2);
            Gizmos.color = Color.blue;
            Gizmos.DrawLine(InputDirectionCompass.transform.position,
                InputDirectionCompass.transform.position + InputDirectionCompass.transform.forward * 2);
        }
    }
}