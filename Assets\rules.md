# Project Architecture and Implementation Rules

This document outlines the architectural principles and implementation rules for the project. These guidelines should be followed when implementing or modifying code to maintain consistency, modularity, and performance.

# Game Overview
 the game is a top down hybrid-casual game with very fast phase action game and has more than 200 enemis in a scene and can surronding the player circular.

## Architecture Overview

The project follows a hybrid architecture combining:
1. **Hierarchical Component System**: Traditional Unity MonoBehaviour components organized in a hierarchy
2. **Scriptable Object Pattern**: Configuration data stored in ScriptableObjects
3. **Event-Driven Communication**: Modules communicate through events without direct references
4. **Entity Component System (ECS)**: High-performance data-oriented design for specific subsystems

## Core Principles

### 1. Strict Module Independence

- Modules **MUST NOT** have direct references to other modules
- Modules **MUST** communicate through the EventManager using the subscribe/unsubscribe and broadcast patterns
- Each module **MUST** handle its own specific functionality without depending on other modules
- Modules **MUST** implement the appropriate interfaces (IModule, IInputModule, etc.)

### 2. Event-Driven Communication

- Use `EventManager.Subscribe<T>(handler)` to listen for events
- Use `EventManager.Broadcast(new EventType())` to send events
- Always unsubscribe from events in OnDestroy or when no longer needed
- Create well-defined event structures in the Events namespace
- Events should contain only the necessary data for the receiving modules

### 3. Mobile Optimization

- Performance is the first priority, followed by code quality
- Minimize garbage collection by avoiding unnecessary allocations
- Use object pooling for frequently created/destroyed objects
- Optimize rendering and physics operations
- Use ECS for performance-critical systems
- Implement proper refresh rates for modules that need regular updates

### 4. Scriptable Object Configuration

- All configurable data **MUST** be stored in ScriptableObjects
- No hard-coded values in scripts/authoring classes
- Create appropriate ScriptableObject assets for different types of configuration
- Use asset creation menus for easy creation of configuration objects
- Document configuration options with tooltips and headers

### 5. Avoid Redundant Fields

- Do not duplicate data across different components or modules
- Use interfaces to access data from other modules when necessary
- Implement proper data encapsulation with getters/setters
- Use events to notify about data changes instead of polling

### 6. Module Implementation

- Each module **MUST** implement the IModule interface or its derivatives
- Modules **MUST** have an Initialize method that sets up event subscriptions
- Modules **MUST** have an UpdateModule method that handles the module's logic
- Modules **SHOULD** clean up resources and event subscriptions in OnDestroy

### 7. Input Handling

- Input should be processed by the InputModule and distributed to other modules
- Modules that need input should implement INeedInput<CharacterParameter>
- The InputModule should not make assumptions about how input will be used
- Input processing should be optimized for mobile platforms

### 8. ECS Implementation

- Use ECS for systems that benefit from data-oriented design
- Follow Unity DOTS best practices:
  - Components should be pure data containers
  - Systems should contain the logic
  - Use Jobs and Burst compilation for performance
  - Use Entities for lightweight identifiers
- Bridge between MonoBehaviour and ECS using shadow components

## Implementation Guidelines

### Module Communication Example

**Events must be struct

```csharp
// Subscribing to events
private void Start()
{
    EventManager.Subscribe<OnSomeEvent>(OnSomeEventHandler);
}

// Unsubscribing from events
private void OnDestroy()
{
    EventManager.Unsubscribe<OnSomeEvent>(OnSomeEventHandler);
}

// Handling events
private void OnSomeEventHandler(OnSomeEvent eventData)
{
    // Handle the event
}

// Broadcasting events
public void TriggerSomething()
{
    EventManager.Broadcast(new OnSomeEvent(someData));
}

public struct OnSomeEvent
{
    public SomeDataType someData;
}

```

### ScriptableObject Configuration Example

```csharp
[CreateAssetMenu(fileName = "NewConfig", menuName = "MyGame/Configuration/NewConfig")]
public class ConfigurationSO : ScriptableObject
{
    [Header("Basic Settings")]
    [Tooltip("Description of the setting")]
    public float someSetting = 1.0f;
    
    [Header("Advanced Settings")]
    public List<SubConfiguration> subConfigurations;
}
```

### Module Implementation Example

```csharp
public class MyModule : SerializedMonoBehaviour, IModule<MySubState>, INeedInput<CharacterParameter>
{
    [field: SerializeField] public int ControllerIndex { get; private set; }
    [field: SerializeField] public List<MainState> MainState { get; private set; }
    [field: SerializeField] public MySubState SubState { get; private set; }
    [field: SerializeField] public bool HasRefreshRate { get; private set; }
    [field: SerializeField] public float RefreshRate { get; private set; }
    [field: SerializeField] public List<CharacterParameter> InputName { get; private set; }
    
    public void Initialize(int controllerIndex)
    {
        // Subscribe to events
        EventManager.Subscribe<SomeEvent>(OnSomeEvent);
    }
    
    public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
    {
        // Module logic here
    }
    
    public Enum GetModuleSubState() => SubState;
    
    public void HandleInput(Dictionary<CharacterParameter, object> inputValue)
    {
        // Handle input here
    }
    
    private void OnDestroy()
    {
        // Unsubscribe from events
        EventManager.Unsubscribe<SomeEvent>(OnSomeEvent);
    }
}
```

## Best Practices

1. **Documentation**: Document your code with XML comments and meaningful variable names
2. **Error Handling**: Implement proper error handling and validation
3. **Testing**: Write unit tests for critical functionality
4. **Debugging**: Use DebugLogManager for logging instead of direct Debug.Log calls
5. **Versioning**: Follow semantic versioning for module interfaces
6. **Refactoring**: Refactor code to eliminate redundancy and improve clarity
7. **Performance Profiling**: Regularly profile your code to identify bottlenecks

By following these rules and guidelines, we can maintain a clean, modular, and high-performance codebase that is easy to extend and maintain.
