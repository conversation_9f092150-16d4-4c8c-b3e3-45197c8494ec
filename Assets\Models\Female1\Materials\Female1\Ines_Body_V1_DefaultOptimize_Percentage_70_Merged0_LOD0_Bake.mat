%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-4462183521078590092
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Ines_Body_V1_DefaultOptimize_Percentage_70_Merged0_LOD0_Bake
  m_Shader: {fileID: -6465566751694194690, guid: df5bb027d94a6c44bb32b3c31ec1303f,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords:
  - TCP2_MOBILE
  - TCP2_OUTLINE_CONST_SIZE
  - TCP2_REFLECTIONS_FRESNEL
  - TCP2_RIM_LIGHTING_LIGHTMASK
  - TCP2_SHADOW_LIGHT_COLOR
  - TCP2_UV_NORMALS_FULL
  m_InvalidKeywords:
  - _METALLICSPECGLOSSMAP
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 1
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - TransparentDepthPrepass
  - TransparentBackface
  - MOTIONVECTORS
  - RayTracingPrepass
  - TransparentDepthPostpass
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: abfaa564ecf51c941ba11c1b69672115, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: bf9efa1dbd2587f4d99b1768891778a4, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: abfaa564ecf51c941ba11c1b69672115, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: e4ca077373814564987281bc998faa61, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ReflectionTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ShadowBaseMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SpecGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _AddPrecomputedVelocity: 0
    - _AlphaClip: 1
    - _AlphaToMask: 1
    - _Blend: 0
    - _BlendModePreserveSpecular: 1
    - _BumpScale: 1
    - _ClearCoatMask: 0
    - _ClearCoatSmoothness: 0
    - _Cull: 0
    - _Cutoff: 0.5
    - _DetailAlbedoMapScale: 1
    - _DetailNormalMapScale: 1
    - _DirectIntensityOutline: 1
    - _DstBlend: 0
    - _DstBlendAlpha: 0
    - _EmissionChannel: 4
    - _EnvironmentReflections: 1
    - _FresnelMax: 1.5
    - _FresnelMin: 0
    - _GlossMapScale: 0
    - _Glossiness: 0
    - _GlossyReflections: 0
    - _IndirectIntensity: 0
    - _IndirectIntensityOutline: 0
    - _MatCapMaskChannel: 0
    - _MatCapType: 0
    - _Metallic: 1
    - _NormalsSource: 0
    - _NormalsUVType: 0
    - _OcclusionChannel: 0
    - _OcclusionStrength: 1
    - _OutlineLightingType: 0
    - _OutlineLightingTypeURP: 0
    - _OutlineMaxWidth: 1
    - _OutlineMinWidth: 1
    - _OutlinePixelSizeType: 1
    - _OutlineTextureLOD: 5
    - _OutlineTextureType: 0
    - _OutlineWidth: 0
    - _Parallax: 0.005
    - _QueueOffset: 0
    - _RampBands: 4
    - _RampBandsSmoothing: 0.1
    - _RampOffset: 0
    - _RampScale: 1
    - _RampSmoothing: 0.038
    - _RampThreshold: 0.257
    - _RampType: 0
    - _ReceiveShadows: 1
    - _ReceiveShadowsOff: 1
    - _ReflectionMapType: 0
    - _ReflectionSmoothness: 0.5
    - _RenderingMode: 0
    - _RimMax: 1
    - _RimMin: 0.5
    - _ShadowColorLightAtten: 1
    - _SingleIndirectColor: 0
    - _Smoothness: 0.897
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularMapType: 0
    - _SpecularRoughness: 0.5
    - _SpecularToonSize: 0.25
    - _SpecularToonSmoothness: 0.05
    - _SpecularType: 0
    - _SrcBlend: 1
    - _SrcBlendAlpha: 1
    - _Surface: 0
    - _UseAlphaTest: 0
    - _UseEmission: 0
    - _UseFresnelReflections: 1
    - _UseMatCap: 0
    - _UseMatCapMask: 0
    - _UseMobileMode: 1
    - _UseNormalMap: 0
    - _UseOcclusion: 0
    - _UseOutline: 1
    - _UseReflections: 0
    - _UseRim: 0
    - _UseRimLightMask: 1
    - _UseShadowTexture: 0
    - _UseSpecular: 0
    - _WorkflowMode: 1
    - _XRMotionVectorsPass: 1
    - _ZWrite: 1
    m_Colors:
    - _BaseColor: {r: 0.99999994, g: 0.99999994, b: 0.99999994, a: 1}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _MatCapColor: {r: 1, g: 1, b: 1, a: 1}
    - _OutlineColor: {r: 0, g: 0, b: 0, a: 1}
    - _ReflectionColor: {r: 1, g: 1, b: 1, a: 1}
    - _RimColor: {r: 0.8, g: 0.8, b: 0.8, a: 0.5}
    - _SColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _SpecularColor: {r: 0.75, g: 0.75, b: 0.75, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
