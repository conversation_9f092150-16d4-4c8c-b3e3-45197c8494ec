using UnityEngine;
using Unity.Mathematics;
using Sirenix.OdinInspector;
using Events;
using DG.Tweening;
using Module.Mono.Animancer.RealsticFemale;

namespace Controllers
{
    /// <summary>
    /// Centralized rotation manager to prevent conflicts between multiple rotation systems
    /// Coordinates MovementModule, AdvancedUpperBodyRotationController, RagdollAimingController, etc.
    /// </summary>
    public class CentralizedRotationManager : MonoBehaviour
    {
        [Header("Rotation Priority System")]
        [SerializeField] private bool enableCentralizedRotation = true;
        [SerializeField] private RotationPriority currentPriority = RotationPriority.Movement;
        
        [Header("Rotation Settings")]
        [SerializeField] private float maxRotationSpeed = 180f; // Degrees per second
        [SerializeField] private float rotationSmoothTime = 0.2f;
        [SerializeField] private float targetSwitchCooldown = 0.3f;
        [SerializeField] private float proximityThreshold = 2f; // Distance to target for proximity checks
        
        [Header("Debug")]
        [SerializeField] private bool enableDebugLogging = false;
        [SerializeField] private bool showDebugGizmos = true;
        
        // Rotation state
        private float currentRotationAngle;
        private float targetRotationAngle;
        private float rotationVelocity;
        private float lastTargetSwitchTime;
        private Vector3 lastTargetPosition;
        private bool isRotating;
        
        // System references
        private Transform playerTransform;
        private RefactoredMovementModule movementModule;
        private AdvancedUpperBodyRotationController upperBodyController;
        private RootMotion.Demos.RagdollAimingController ragdollAiming;
        
        // Rotation requests
        private RotationRequest currentRequest;
        private bool hasActiveRequest;
        
        public enum RotationPriority
        {
            Movement = 0,           // Normal movement rotation
            Aiming = 1,            // Aiming at targets
            RagdollAiming = 2,     // RagdollAimingController
            FastRotation = 3,      // Emergency fast rotation
            Override = 4           // Manual override
        }
        
        [System.Serializable]
        public struct RotationRequest
        {
            public RotationPriority priority;
            public Vector3 targetPosition;
            public float rotationSpeed;
            public bool useSmoothing;
            public string requestSource;
            public float requestTime;
            
            public bool IsValid => !targetPosition.Equals(Vector3.zero) && requestTime > 0;
        }
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeRotationManager();
        }
        
        private void Start()
        {
            SetupSystemReferences();
            SubscribeToEvents();
        }
        
        private void Update()
        {
            if (enableCentralizedRotation)
            {
                UpdateRotationSystem();
            }
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        #endregion
        
        #region Initialization
        
        private void InitializeRotationManager()
        {
            playerTransform = transform;
            currentRotationAngle = playerTransform.eulerAngles.y;
            targetRotationAngle = currentRotationAngle;
            
            LogDebug("CentralizedRotationManager initialized");
        }
        
        private void SetupSystemReferences()
        {
            movementModule = GetComponent<RefactoredMovementModule>();
            upperBodyController = GetComponent<AdvancedUpperBodyRotationController>();
            ragdollAiming = GetComponentInChildren<RootMotion.Demos.RagdollAimingController>();
            
            LogDebug($"System references: Movement={movementModule != null}, UpperBody={upperBodyController != null}, Ragdoll={ragdollAiming != null}");
        }
        
        #endregion
        
        #region Event Handling
        
        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnRequireFastRotationEvent>(OnFastRotationRequired);
        }
        
        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnFastRotationRequired);
        }
        
        private void OnAimingStarted(OnAimingOnTargetEvent eventData)
        {
            RequestRotation(RotationPriority.Aiming, eventData.TargetPosition, maxRotationSpeed, true, "AimingModule");
        }
        
        private void OnAimingStopped(OnUnAimingTargetEvent eventData)
        {
            ClearRotationRequest(RotationPriority.Aiming);
        }
        
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            // Only update if we're already aiming or if this is a higher priority
            if (currentPriority <= RotationPriority.Aiming)
            {
                RequestRotation(RotationPriority.Aiming, eventData.TargetPosition, maxRotationSpeed, true, "TargetDetection");
            }
        }
        
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            ClearRotationRequest(RotationPriority.Aiming);
        }
        
        private void OnFastRotationRequired(OnRequireFastRotationEvent eventData)
        {
            RequestRotation(RotationPriority.FastRotation, eventData.TargetPosition, maxRotationSpeed * 2f, false, "FastRotation");
        }
        
        #endregion
        
        #region Rotation Management
        
        /// <summary>
        /// Request rotation with priority system
        /// </summary>
        public bool RequestRotation(RotationPriority priority, Vector3 targetPosition, float speed, bool useSmoothing, string source)
        {
            // Check if we should accept this request
            if (!ShouldAcceptRotationRequest(priority, targetPosition))
            {
                LogDebug($"Rotation request rejected: Priority={priority}, Source={source}");
                return false;
            }
            
            // Create new rotation request
            currentRequest = new RotationRequest
            {
                priority = priority,
                targetPosition = targetPosition,
                rotationSpeed = speed,
                useSmoothing = useSmoothing,
                requestSource = source,
                requestTime = Time.time
            };
            
            hasActiveRequest = true;
            currentPriority = priority;
            lastTargetPosition = targetPosition;
            lastTargetSwitchTime = Time.time;
            
            LogDebug($"Rotation request accepted: Priority={priority}, Target={targetPosition}, Source={source}");
            return true;
        }
        
        /// <summary>
        /// Clear rotation request for specific priority
        /// </summary>
        public void ClearRotationRequest(RotationPriority priority)
        {
            if (hasActiveRequest && currentRequest.priority == priority)
            {
                hasActiveRequest = false;
                currentPriority = RotationPriority.Movement;
                LogDebug($"Rotation request cleared: Priority={priority}");
            }
        }
        
        /// <summary>
        /// Check if we should accept a rotation request
        /// </summary>
        private bool ShouldAcceptRotationRequest(RotationPriority priority, Vector3 targetPosition)
        {
            // Always accept higher priority requests
            if (priority > currentPriority)
                return true;
            
            // Check cooldown for same priority
            if (priority == currentPriority && Time.time - lastTargetSwitchTime < targetSwitchCooldown)
            {
                // Allow small target updates without cooldown
                if (Vector3.Distance(lastTargetPosition, targetPosition) < 0.5f)
                    return true;
                
                return false;
            }
            
            // Check if RagdollAiming is active and should take priority
            if (IsRagdollAimingActive() && priority < RotationPriority.RagdollAiming)
                return false;
            
            // Check proximity to prevent spinning around close targets
            float distanceToTarget = Vector3.Distance(playerTransform.position, targetPosition);
            if (distanceToTarget < proximityThreshold && priority == RotationPriority.Aiming)
            {
                LogDebug($"Target too close ({distanceToTarget:F2}m), rejecting rotation request");
                return false;
            }
            
            return true;
        }
        
        /// <summary>
        /// Update the centralized rotation system
        /// </summary>
        private void UpdateRotationSystem()
        {
            // Check if RagdollAiming should take control
            if (IsRagdollAimingActive())
            {
                DisableOtherRotationSystems();
                return;
            }
            
            // Process current rotation request
            if (hasActiveRequest && currentRequest.IsValid)
            {
                ProcessRotationRequest();
            }
            else
            {
                // No active request, allow normal movement rotation
                EnableMovementRotation();
            }
        }
        
        /// <summary>
        /// Process the current rotation request
        /// </summary>
        private void ProcessRotationRequest()
        {
            Vector3 directionToTarget = currentRequest.targetPosition - playerTransform.position;
            directionToTarget.y = 0; // Keep horizontal
            
            if (directionToTarget.magnitude < 0.1f)
                return;
            
            // Calculate target angle
            float targetAngle = Mathf.Atan2(directionToTarget.x, directionToTarget.z) * Mathf.Rad2Deg;
            
            // Apply rotation speed limiting
            float maxAngleChange = currentRequest.rotationSpeed * Time.deltaTime;
            float angleDifference = Mathf.DeltaAngle(currentRotationAngle, targetAngle);
            
            if (Mathf.Abs(angleDifference) > maxAngleChange)
            {
                targetAngle = currentRotationAngle + Mathf.Sign(angleDifference) * maxAngleChange;
            }
            
            // Apply rotation
            if (currentRequest.useSmoothing)
            {
                currentRotationAngle = Mathf.SmoothDampAngle(currentRotationAngle, targetAngle, ref rotationVelocity, rotationSmoothTime);
            }
            else
            {
                currentRotationAngle = targetAngle;
            }
            
            // Apply to transform
            playerTransform.rotation = Quaternion.Euler(0, currentRotationAngle, 0);
            isRotating = true;
            
            // Disable other rotation systems
            DisableOtherRotationSystems();
            
            LogDebug($"Centralized rotation applied: {currentRotationAngle:F1}° (Target: {targetAngle:F1}°)");
        }
        
        #endregion
        
        #region System Coordination
        
        /// <summary>
        /// Check if RagdollAiming is active
        /// </summary>
        private bool IsRagdollAimingActive()
        {
            return ragdollAiming != null && ragdollAiming.enabled && ragdollAiming.weight > 0.1f;
        }
        
        /// <summary>
        /// Disable other rotation systems when centralized rotation is active
        /// </summary>
        private void DisableOtherRotationSystems()
        {
            // Disable MovementModule rotation
            if (movementModule != null)
            {
                // MovementModule should check if centralized rotation is active
                // This will be implemented in MovementModule integration
            }
            
            // Disable AdvancedUpperBodyRotationController player rotation
            if (upperBodyController != null)
            {
                upperBodyController.SetRotateEntirePlayer(false);
            }
        }
        
        /// <summary>
        /// Enable normal movement rotation
        /// </summary>
        private void EnableMovementRotation()
        {
            if (upperBodyController != null)
            {
                upperBodyController.SetRotateEntirePlayer(true);
            }
            
            isRotating = false;
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Check if centralized rotation is currently active
        /// </summary>
        public bool IsCentralizedRotationActive()
        {
            return enableCentralizedRotation && (hasActiveRequest || IsRagdollAimingActive());
        }
        
        /// <summary>
        /// Get current rotation status
        /// </summary>
        public string GetRotationStatus()
        {
            return $"Priority: {currentPriority}, Active: {hasActiveRequest}, Rotating: {isRotating}, " +
                   $"Current: {currentRotationAngle:F1}°, Target: {targetRotationAngle:F1}°";
        }
        
        /// <summary>
        /// Force stop all rotation
        /// </summary>
        public void StopAllRotation()
        {
            hasActiveRequest = false;
            currentPriority = RotationPriority.Movement;
            isRotating = false;
            EnableMovementRotation();
            LogDebug("All rotation stopped");
        }
        
        #endregion
        
        #region Debug
        
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[CentralizedRotationManager] {message}");
            }
        }
        
        private void OnDrawGizmosSelected()
        {
            if (!showDebugGizmos || !Application.isPlaying) return;
            
            Vector3 center = transform.position + Vector3.up * 2f;
            
            // Draw current rotation
            Gizmos.color = Color.green;
            Vector3 currentDir = Quaternion.Euler(0, currentRotationAngle, 0) * Vector3.forward;
            Gizmos.DrawLine(center, center + currentDir * 2f);
            
            // Draw target position
            if (hasActiveRequest)
            {
                Gizmos.color = Color.red;
                Gizmos.DrawWireSphere(currentRequest.targetPosition, 0.3f);
                Gizmos.DrawLine(center, currentRequest.targetPosition);
                
                // Draw proximity threshold
                Gizmos.color = Color.yellow;
                Gizmos.DrawWireSphere(transform.position, proximityThreshold);
            }
        }
        
        #endregion
    }
}
