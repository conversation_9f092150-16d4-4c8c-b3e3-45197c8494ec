%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-7420771746182877986
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!114 &-5137072891509711178
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 639247ca83abc874e893eb93af2b5e44, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 0
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 32
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: M_Creature_Mutant
  m_Shader: {fileID: 4800000, guid: 1e8425e1846f52e4e8d8171ccadbcb29, type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 1
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AnimatedBoneMatrices:
        m_Texture: {fileID: 2800000, guid: 58bf03d60e86d8747ae0e6ae237f55de, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Base:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseColorMap:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 5c3b287335ece59489797577cd1f645b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailAlbedoMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MaskMap:
        m_Texture: {fileID: 2800000, guid: 58a4332ec6d01304587470f191b29470, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MatCapTex:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Normal:
        m_Texture: {fileID: 2800000, guid: 5c3b287335ece59489797577cd1f645b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _NormalMap:
        m_Texture: {fileID: 2800000, guid: 5c3b287335ece59489797577cd1f645b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ParallaxMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Ramp:
        m_Texture: {fileID: 2800000, guid: ccad9b0732473ee4e95de81e50e9050f, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_13c59d87d54f4d1995ab2f5b033f8b75_Texture_1:
        m_Texture: {fileID: 2800000, guid: 9677fc9f7c095ce4ab85d2e438f7a9d1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_13c59d87d54f4d1995ab2f5b033f8b75_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 9677fc9f7c095ce4ab85d2e438f7a9d1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_1aadf4b8539740d9921f298e1f04e1c1_Texture_1:
        m_Texture: {fileID: 2800000, guid: 5c3b287335ece59489797577cd1f645b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_1aadf4b8539740d9921f298e1f04e1c1_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 5c3b287335ece59489797577cd1f645b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_1e1aca5121be47c9bd99ede7a4f26289_Texture_1:
        m_Texture: {fileID: 2800000, guid: c2b4d6f931b1de648a014531c0b3bc40, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_1e1aca5121be47c9bd99ede7a4f26289_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: c2b4d6f931b1de648a014531c0b3bc40, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_4437c13238904ecdba201ae55393a156_Texture_1:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_4437c13238904ecdba201ae55393a156_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_80030b6e19304a0199cfc486eda2b9fc_Texture_1:
        m_Texture: {fileID: 2800000, guid: e12708a4b8b93614a875a52530686ed1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_80030b6e19304a0199cfc486eda2b9fc_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: e12708a4b8b93614a875a52530686ed1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_cb06f7f7c29d4707861cc99f3dde8e59_Texture_1:
        m_Texture: {fileID: 2800000, guid: 58bf03d60e86d8747ae0e6ae237f55de, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_cb06f7f7c29d4707861cc99f3dde8e59_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 58bf03d60e86d8747ae0e6ae237f55de, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_f124648f61d34d92864dba029c6dfb6d_Texture_1:
        m_Texture: {fileID: 2800000, guid: 58a4332ec6d01304587470f191b29470, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_f124648f61d34d92864dba029c6dfb6d_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 58a4332ec6d01304587470f191b29470, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_f882791fb821474cbd713ddf999b2b38_Texture_1:
        m_Texture: {fileID: 2800000, guid: 0d69e211be76c014982989823f378094, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SampleTexture2D_f882791fb821474cbd713ddf999b2b38_Texture_1_Texture2D:
        m_Texture: {fileID: 2800000, guid: 0d69e211be76c014982989823f378094, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SketchTexture:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _StylizedThreshold:
        m_Texture: {fileID: 2800000, guid: 4f249b1d66e487c46aac691f1234b4f5, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture:
        m_Texture: {fileID: 2800000, guid: 93f2c70f3e565e34ba6fcff5c8d30fbd, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - _BUILTIN_QueueControl: 0
    - _BUILTIN_QueueOffset: 0
    - _BumpScale: 1
    - _Cavity: 1
    - _Color_or_texture: 0.357
    - _Curvature: 0.657
    - _Curvature_2: 0.344
    - _Cutoff: 0.5
    - _DeformedMeshIndex: 0
    - _DetailNormalMapScale: 1
    - _DstBlend: 0
    - _EnableAnimation: 0
    - _Float: 0
    - _GlossMapScale: 1
    - _Glossiness: 0.5
    - _GlossyReflections: 1
    - _Horn_2: 1
    - _Metallic: 0
    - _Mode: 0
    - _OcclusionStrength: 1
    - _Parallax: 0.02
    - _QueueControl: 0
    - _QueueOffset: 0
    - _RampSmoothing: 0.5
    - _RampTextureOffset: 0.651
    - _RampTextureSize: 1
    - _RampThreshold: 0.835
    - _ReceiveShadowsOff: 1
    - _RimMax: 2
    - _RimMaxVert: 1.183
    - _RimMin: 0.73
    - _RimMinVert: 0.183
    - _Roug: 0.984
    - _ShadowLineSmoothing: 0.0701
    - _ShadowLineStrength: 10
    - _ShadowLineThreshold: 0.952
    - _SketchTexture_OffsetSpeed: 120
    - _Smoothness: 0
    - _SmoothnessTextureChannel: 0
    - _SpecularHighlights: 1
    - _SpecularSmoothness: 0.2
    - _SpecularToonBands: 2
    - _SrcBlend: 1
    - _Tile_texture: 4.39
    - _UVSec: 0
    - _XRMotionVectorsPass: 1
    - _ZWrite: 1
    - __BeginGroup_ShadowHSV: 1
    - __EndGroup: 0
    - __dummy__: 0
    m_Colors:
    - _Base: {r: 0.6886792, g: 0.6886792, b: 0.6886792, a: 0}
    - _BaseColor: {r: 1, g: 1, b: 1, a: 0}
    - _Cavity_C: {r: 0, g: 0, b: 0, a: 0}
    - _Chest: {r: 1, g: 1, b: 1, a: 0}
    - _Color: {r: 1, g: 1, b: 1, a: 1}
    - _Curvature_C: {r: 1, g: 1, b: 1, a: 0}
    - _Curvature_C2: {r: 0, g: 0, b: 0, a: 0}
    - _DeformationParamsForMotionVectors: {r: 0, g: 0, b: 0, a: 0}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _HColor: {r: 1, g: 1, b: 1, a: 1}
    - _Horn: {r: 1, g: 1, b: 1, a: 0}
    - _Horn_2C: {r: 0.1981132, g: 0.1981132, b: 0.1981132, a: 0}
    - _Leg: {r: 0.20754719, g: 0.20754719, b: 0.20754719, a: 0}
    - _Leg_2: {r: 1, g: 1, b: 1, a: 0}
    - _MatCapColor: {r: 0.7264151, g: 0.7264151, b: 0.7264151, a: 1}
    - _RimColor: {r: 1, g: 1, b: 1, a: 0.5}
    - _SColor: {r: 0.19999996, g: 0.19999996, b: 0.19999996, a: 1}
    - _ShadowLineColor: {r: 0, g: 0, b: 0, a: 1}
    - _SpecularColor: {r: 1, g: 1, b: 1, a: 1}
    - _Teeth: {r: 1, g: 1, b: 1, a: 0}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
