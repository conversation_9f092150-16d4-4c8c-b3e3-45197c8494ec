%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!1 &33882673938384918
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3984984611843329466}
  m_Layer: 0
  m_Name: upperarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3984984611843329466
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 33882673938384918}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000017129787, y: 0.00000023047708, z: -0.0000014104878,
    w: 1}
  m_LocalPosition: {x: -0.00000030517577, y: 0.14066535, z: -0.0000000047683715}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4149227583924101474}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &62624355813696969
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5865011514679355812}
  m_Layer: 0
  m_Name: index_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5865011514679355812
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 62624355813696969}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000066359403, y: 0.005316494, z: -0.006927475, w: 0.9999619}
  m_LocalPosition: {x: -0.00000015258789, y: 0.038483657, z: 0.00000021457672}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1502515452299025953}
  m_Father: {fileID: 5211065926650188693}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &88989904509397248
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3044181435835765161}
  - component: {fileID: 8842457008472324467}
  m_Layer: 8
  m_Name: MP4AimingSubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3044181435835765161
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 88989904509397248}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5089675981995570609}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8842457008472324467
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 88989904509397248}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 583b0b8f3bcc4bf6b0e1be30c2aa1e2c, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AimingData:
    HasLeftHandIK: 1
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
--- !u!1 &92631648471143880
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3717996316510139074}
  m_Layer: 0
  m_Name: ring_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3717996316510139074
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 92631648471143880}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0036970049, y: -0.0002580989, z: -0.000032584452, w: 0.99999315}
  m_LocalPosition: {x: 0.00000015258789, y: 0.042684708, z: -0.000000009536743}
  m_LocalScale: {x: 1.0000001, y: 1.0000004, z: 1.0000004}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3790389470756885847}
  m_Father: {fileID: 4797740664379296513}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &132733568285822979
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4777220526592169751}
  - component: {fileID: 9213730673134067178}
  m_Layer: 0
  m_Name: spine_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4777220526592169751
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132733568285822979}
  serializedVersion: 2
  m_LocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268, w: 0.9867998}
  m_LocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
  m_LocalScale: {x: 1, y: 1.0000002, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4303522748295484652}
  m_Father: {fileID: 5928552661432004395}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &9213730673134067178
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 132733568285822979}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268, w: 0.9867998}
  limit: 35
  twistLimit: 35
--- !u!1 &197955182703635422
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2837586148865164468}
  - component: {fileID: 1032181480334536729}
  m_Layer: 8
  m_Name: CharacterPositionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2837586148865164468
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 197955182703635422}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5466703835676033078}
  - {fileID: 47519501808106740}
  - {fileID: 7678416043862309964}
  - {fileID: 3341596775319770571}
  - {fileID: 3867368653293550154}
  - {fileID: 194924033231347926}
  - {fileID: 7024792088366721414}
  - {fileID: 1791615436228211716}
  - {fileID: 5706714760049950198}
  - {fileID: 3899082658436753149}
  - {fileID: 1406359692948966899}
  - {fileID: 8651355727699331694}
  - {fileID: 744890345733490932}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1032181480334536729
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 197955182703635422}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aa8b96fd653afbe428a148130cdf67b2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  parent: {fileID: 4378843590597608864}
  worldPositionStays: 1
--- !u!1 &229480934139786986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1479284470707564362}
  m_Layer: 8
  m_Name: GroundRaycaster3
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1479284470707564362
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 229480934139786986}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: -0.00000016485335, y: 0, z: -0.26091012}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3341596775319770571}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &246997015415355911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7934703814139299791}
  - component: {fileID: 5811028977162558912}
  m_Layer: 8
  m_Name: mag 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7934703814139299791
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 246997015415355911}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4970681250608070657}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5811028977162558912
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 246997015415355911}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7371548420364037637, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 5945311810865105750}
  - {fileID: 2381486250098498953}
  - {fileID: 2423114747879000155}
  - {fileID: 1243558745997792308}
  - {fileID: 7904709490601092112}
  - {fileID: 4495432798587655918}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2423114747879000155}
  m_AABB:
    m_Center: {x: 0, y: -0.08626072, z: -0.014301309}
    m_Extent: {x: 0.014163713, y: 0.059781164, z: 0.026841497}
  m_DirtyAABB: 0
--- !u!1 &272476959374831574
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4605998933334557323}
  m_Layer: 0
  m_Name: clavicle_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4605998933334557323
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 272476959374831574}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0058371807, y: -0.17127486, z: 0.6711394, w: 0.72125083}
  m_LocalPosition: {x: -0.04634696, y: 0.2128839, z: 0.004223671}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4149227583924101474}
  m_Father: {fileID: 2941098901988364380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &337985612820022052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1406359692948966899}
  m_Layer: 8
  m_Name: FloorAngleBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1406359692948966899
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 337985612820022052}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0.373}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6128157687943413941}
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &342848200258455792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4053749872605063169}
  m_Layer: 0
  m_Name: pinky_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4053749872605063169
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 342848200258455792}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0070864474, y: 0.0012980792, z: 0.00002894654, w: 0.9999741}
  m_LocalPosition: {x: 0.00000030517577, y: 0.033087693, z: -0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5078890076475787187}
  m_Father: {fileID: 1481966550462016051}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &388989701412483330
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7556803309533903411}
  m_Layer: 0
  m_Name: thumb_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7556803309533903411
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 388989701412483330}
  serializedVersion: 2
  m_LocalRotation: {x: 0.3104095, y: -0.12205748, z: -0.096656166, w: 0.93776625}
  m_LocalPosition: {x: 0.008959808, y: 0.022829894, z: 0.018451862}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9220517768640357345}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &396472454509773591
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 394745373956028053}
  m_Layer: 0
  m_Name: CC_Base_Teeth02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &394745373956028053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 396472454509773591}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00022650788, z: -0.00009729427, w: -0.00008053859}
  m_LocalPosition: {x: -0.024823641, y: 0.012739562, z: 0.00014736093}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9130952369729836117}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &447036434987798277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6128157687943413941}
  m_Layer: 8
  m_Name: FloorAngleHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6128157687943413941
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 447036434987798277}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: -0.00000016292068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1406359692948966899}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &576534037751556055
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7974120674929081353}
  - component: {fileID: 6602727797948381583}
  m_Layer: 8
  m_Name: slide 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7974120674929081353
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576534037751556055}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4970681250608070657}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &6602727797948381583
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 576534037751556055}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8380061810295591180, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 5945311810865105750}
  - {fileID: 2381486250098498953}
  - {fileID: 2423114747879000155}
  - {fileID: 1243558745997792308}
  - {fileID: 7904709490601092112}
  - {fileID: 4495432798587655918}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2423114747879000155}
  m_AABB:
    m_Center: {x: 0, y: -0.009797938, z: 0.06299989}
    m_Extent: {x: 0.01610092, y: 0.023823507, z: 0.10387771}
  m_DirtyAABB: 0
--- !u!1 &684061795519196911
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3484386517536547667}
  m_Layer: 0
  m_Name: thigh_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3484386517536547667
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 684061795519196911}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000008643836, y: 0.0000018727438, z: -6.0897634e-21, w: 1}
  m_LocalPosition: {x: -0.0000002813339, y: 0.23965019, z: -0.00000030994414}
  m_LocalScale: {x: 1.0000002, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2993532018169991005}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &746174666653944226
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 194924033231347926}
  m_Layer: 8
  m_Name: InteractionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &194924033231347926
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 746174666653944226}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &747506512208818193
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 119864837713895696}
  - component: {fileID: 2863726569885860666}
  - component: {fileID: 5530659526549608096}
  - component: {fileID: 3672128142939602080}
  m_Layer: 9
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &119864837713895696
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747506512208818193}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00548152, y: 0.00012282522, z: -0.022256007, w: 0.99973726}
  m_LocalPosition: {x: -0.00000054202985, y: 0.4793005, z: -0.0000004945031}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4612088608534634171}
  m_Father: {fileID: 4410766268693251201}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2863726569885860666
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747506512208818193}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &5530659526549608096
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747506512208818193}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705828
  m_Height: 0.54307145
  m_Direction: 1
  m_Center: {x: 0.00000021979207, y: 0.24685049, z: -0.0000003026798}
--- !u!153 &3672128142939602080
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 747506512208818193}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 1970453879693082122}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.62689
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.37311
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &758288085183471182
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3936308218292781600}
  m_Layer: 8
  m_Name: ForwardAimTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3936308218292781600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758288085183471182}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2331077866999755286}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &774443134923109320
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7234163297638815730}
  m_Layer: 0
  m_Name: CC_Base_UpperJaw
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7234163297638815730
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 774443134923109320}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00005004184, y: 0.0000016683813, z: 1, w: 0.000009986938}
  m_LocalPosition: {x: 0.057560336, y: 0.018850708, z: 0.000037461232}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7299991780694236420}
  m_Father: {fileID: 4490211272603021980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &971315666028710933
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 502270518393233424}
  - component: {fileID: 5631959735350184254}
  m_Layer: 8
  m_Name: PistolState
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &502270518393233424
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971315666028710933}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7247426993471718996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5631959735350184254
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 971315666028710933}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: be59ca64414230e4da448b03211d36ca, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: m_OnEndAnimationEvent
      Entry: 6
      Data: 
  m_aimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  _Character: {fileID: 7811239351625802803}
  WeaponGameObject: {fileID: 6406834782533140737}
  Equip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400150, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  UnEquip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400152, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  Idle:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 5bba91547eae2884f8b5638e2d2bd50d, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Aiming:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 636779f40299e34448c16e17b1d69899, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Shooting:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 636779f40299e34448c16e17b1d69899, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Reloading:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400096, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  enableEventBroadcasting: 1
  enableUpperBodyAnimations: 1
  enableWeaponObjectAnimations: 1
  enableDebugLogging: 0
--- !u!1 &1027854070588054816
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4099452736357410725}
  - component: {fileID: 3393522759107121190}
  - component: {fileID: 6359503661540266304}
  - component: {fileID: 4298143726018690048}
  m_Layer: 9
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4099452736357410725
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027854070588054816}
  serializedVersion: 2
  m_LocalRotation: {x: -0.02410228, y: -0.028923262, z: -0.7123166, w: 0.7008478}
  m_LocalPosition: {x: 0.14615616, y: 0.33055305, z: -0.08785249}
  m_LocalScale: {x: 0.99999976, y: 0.9999995, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 700223709043250698}
  m_Father: {fileID: 6475188008645147268}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3393522759107121190
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027854070588054816}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &6359503661540266304
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027854070588054816}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08442511
  m_Height: 0.30955872
  m_Direction: 1
  m_Center: {x: -0.00000006286429, y: 0.14070858, z: 0.00000039115562}
--- !u!153 &4298143726018690048
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1027854070588054816}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4016410368822921293}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1068563374679353460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1118397962452466628}
  - component: {fileID: 7417124975178428051}
  - component: {fileID: 3112539510873828825}
  m_Layer: 0
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1118397962452466628
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1068563374679353460}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: -0.00000042438506, y: 0.4937627, z: -0.0000005340576}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4056800010831387403}
  - {fileID: 812914778976819066}
  m_Father: {fileID: 6553216298185710679}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7417124975178428051
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1068563374679353460}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &3112539510873828825
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1068563374679353460}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3344840827136345536}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1069575628876630517
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5521858126448997138}
  m_Layer: 0
  m_Name: calf_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5521858126448997138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1069575628876630517}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000016143078, y: 0.0001604101, z: 0.000012576289, w: 1}
  m_LocalPosition: {x: -0.00000021934508, y: 0.24688132, z: -0.0000002670288}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 6553216298185710679}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1098722968554540782
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9074641442115584318}
  m_Layer: 8
  m_Name: Aim
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9074641442115584318
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1098722968554540782}
  serializedVersion: 2
  m_LocalRotation: {x: -0.703968, y: -0.7074759, z: -0.040998396, w: 0.04718122}
  m_LocalPosition: {x: -0.016999971, y: 0.0030000117, z: 0.007999996}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7780519408018376221}
  - {fileID: 6159669958262507441}
  m_Father: {fileID: 3645407059040468473}
  m_LocalEulerAnglesHint: {x: -172.852, y: 0.5220032, z: -90.31702}
--- !u!1 &1116596369228809365
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9039848297957138845}
  - component: {fileID: 5166316140777864743}
  - component: {fileID: 148063521457271912}
  - component: {fileID: 6109271867980557725}
  m_Layer: 9
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9039848297957138845
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1116596369228809365}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0051458613, y: -0.00012410856, z: 0.024217186, w: 0.99969345}
  m_LocalPosition: {x: 0.0000002104789, y: 0.4792871, z: -0.00000013649695}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1845394404451702405}
  m_Father: {fileID: 5647550049040330743}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5166316140777864743
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1116596369228809365}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &148063521457271912
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1116596369228809365}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705563
  m_Height: 0.5431391
  m_Direction: 1
  m_Center: {x: -0.00000021420419, y: 0.24688114, z: -0.00000020395967}
--- !u!153 &6109271867980557725
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1116596369228809365}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5769118334450509500}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.837384
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.16261
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1129696503693302981
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4161593320679618462}
  m_Layer: 8
  m_Name: GroundRaycaster4
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4161593320679618462
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1129696503693302981}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: -0.16829455, y: 0, z: 0.00000015692554}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3341596775319770571}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1227510079566460888
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7780519408018376221}
  - component: {fileID: 7802067574438367928}
  - component: {fileID: 831717835851510453}
  m_Layer: 8
  m_Name: Laser1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7780519408018376221
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1227510079566460888}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000002, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9074641442115584318}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &7802067574438367928
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1227510079566460888}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &831717835851510453
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1227510079566460888}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1269183755302226277
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7402654799015674516}
  - component: {fileID: 7138319810809311850}
  - component: {fileID: 1330705101648466629}
  - component: {fileID: 3764657792611797983}
  - component: {fileID: 5996753077850343230}
  - component: {fileID: 3566399340842259981}
  - component: {fileID: 1783523751003591863}
  - component: {fileID: 2962463163339834589}
  - component: {fileID: 368280542376683019}
  m_Layer: 8
  m_Name: InteractionModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7402654799015674516
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2195793653869371303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7138319810809311850
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c08dd31e7f694fdcaf3d250569c31167, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_interactor: {fileID: 3764657792611797983}
  <InputName>k__BackingField: 14000000
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 
  _interactionSubState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  m_isInteracting: 0
--- !u!135 &1330705101648466629
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 2097152
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1.3668647
  m_Center: {x: 0, y: 0.84438926, z: 0}
--- !u!114 &3764657792611797983
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c250986fe58e08148abc5e673e3e4fe4, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  anyConnected: 0
  effectorLinks:
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: LH
    effectorType: 5
    posOffset: {x: -0.141, y: 0.624, z: -0.046}
    angleOffset: -10
    angleXZ: 130
    angleOffsetYZ: 30
    angleYZ: 120
    maxRadius: 0.6708473
    minRadius: 0.2012542
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: RH
    effectorType: 6
    posOffset: {x: 0.141, y: 0.624, z: -0.046}
    angleOffset: 60
    angleXZ: 130
    angleOffsetYZ: 30
    angleYZ: 120
    maxRadius: 0.67085
    minRadius: 0.20125501
  - targetActive: 0
    targetPosition: {x: 0, y: 0, z: 0}
    effectorWorldSpace: {x: 0, y: 0, z: 0}
    enabled: 1
    effectorName: Body
    effectorType: 0
    posOffset: {x: 0, y: 0, z: 0}
    angleOffset: 0
    angleXZ: 45
    angleOffsetYZ: 0
    angleYZ: 45
    maxRadius: 0.1
    minRadius: 0.05
  sphereCol: {fileID: 1330705101648466629}
  sphereColWithRotScale: {x: 0, y: 0, z: 0}
  selfInteractionObject: {fileID: 0}
  selfInteractionEnabled: 0
  selectedByUI: 0
  checkOncePerObject: 0
  layerName: Player
  playerRigidbody: {fileID: 0}
  playerCollider: {fileID: 0}
  playerTransform: {fileID: 0}
  interactionStates: {fileID: 0}
  armLength: 0
  vehicleInput: {fileID: 0}
  vehiclePartCont: {fileID: 0}
  childTurrets: []
  vehiclePartsActive: 0
  lookAtTargetEnabled: 1
  waitForNewTarget: 0
  lookEndTimer: 0
  lookInitiateFailed: 0
  alternateHead: {fileID: 0}
  raycastDistance: 20
  debug: 1
  selectedTab: 0
  savePath: Assets/Interactor/SettelaInteractor.asset
  maxRadius: 0
  logoChange: 0
  opacity: 1
--- !u!114 &5996753077850343230
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 2491691ef2f702a4598b3c2bac8d7109, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 3764657792611797983}
  animator: {fileID: 0}
  minHeight: 0
  midHeight: 1.2
  maxHeight: 2.2
  minDist: 0
  maxDist: 0.75
  maxMargin: 0.05
  crouchSetStart: 0.5
  initiated: 0
  clipPlaying: 0
  crouchWeightNormalized: 0
  debug: 1
  debugOrbitalPositioner: {fileID: 0}
  debugMirror: 0
  debugDuration: 0
  debugOrbitalValues: {x: 0, y: 0, z: 0}
--- !u!114 &3566399340842259981
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6430f5b64864c57459b325f15e863985, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _animator: {fileID: 0}
  isHumanoid: 1
  ikParts:
  - part: 4
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  - part: 2
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  - part: 3
    matchChildBones: 1
    fixWristDeformation: 0
    excludeFromBones: []
    currentTarget: {fileID: 0}
    weight: 0
    rootBone: {fileID: 0}
    midBone: {fileID: 0}
    tipBone: {fileID: 0}
    hint: {fileID: 0}
    rootRotationOffset: 0
    midRotationOffset: 0
    targetDuration: 0
    backDuration: 0
    pause: 0
    waitForReset: 0
    boneTransform: {fileID: 0}
    enabled: 0
    interrupt: 0
    childBones: []
    weightedPosition: {x: 0, y: 0, z: 0}
    positionBeforeIK: {x: 0, y: 0, z: 0}
    rotationBeforeIK: {x: 0, y: 0, z: 0, w: 0}
  lookEnabled: 1
  lookTarget: {fileID: 0}
  lookWeight: 0
  headBone: {fileID: 0}
  lastHeadDirection: {x: 0, y: 0, z: 0}
--- !u!114 &1783523751003591863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ce41d25b1d506af47a851432967add80, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  interactor: {fileID: 3764657792611797983}
  animator: {fileID: 0}
  animationClips:
  - {fileID: 7400000, guid: 77042cc464d967d46817dcca4dcadacf, type: 3}
  - {fileID: 7400000, guid: 7f162e3905b4b4b458678ab6cdaf1a20, type: 3}
  preventSpam: 1
--- !u!114 &2962463163339834589
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a70e525c82ce9413fa4d940ad7fcf1db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  references:
    root: {fileID: 7402654799015674516}
    pelvis: {fileID: 0}
    leftThigh: {fileID: 0}
    leftCalf: {fileID: 0}
    leftFoot: {fileID: 0}
    rightThigh: {fileID: 0}
    rightCalf: {fileID: 0}
    rightFoot: {fileID: 0}
    leftUpperArm: {fileID: 0}
    leftForearm: {fileID: 0}
    leftHand: {fileID: 0}
    rightUpperArm: {fileID: 0}
    rightForearm: {fileID: 0}
    rightHand: {fileID: 0}
    head: {fileID: 0}
    spine:
    - {fileID: 0}
    - {fileID: 0}
    eyes: []
  solver:
    executedInEditor: 0
    IKPosition: {x: 0, y: 0, z: 0}
    IKPositionWeight: 1
    root: {fileID: 0}
    iterations: 4
    chain:
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 01000000020000000300000004000000
      childConstraints:
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 0}
        bone2: {fileID: 0}
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 1
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 1
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 0}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    effectors:
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones:
      - {fileID: 0}
      - {fileID: 0}
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 0}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    spineMapping:
      spineBones:
      - {fileID: 0}
      - {fileID: 0}
      - {fileID: 0}
      leftUpperArmBone: {fileID: 0}
      rightUpperArmBone: {fileID: 0}
      leftThighBone: {fileID: 0}
      rightThighBone: {fileID: 0}
      iterations: 3
      twistWeight: 1
    boneMappings:
    - bone: {fileID: 0}
      maintainRotationWeight: 0
    limbMappings:
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 1
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 0}
      bone2: {fileID: 0}
      bone3: {fileID: 0}
      maintainRotationWeight: 1
      weight: 1
    FABRIKPass: 1
    rootNode: {fileID: 0}
    spineStiffness: 0.5
    pullBodyVertical: 0.5
    pullBodyHorizontal: 0
--- !u!114 &368280542376683019
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1269183755302226277}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 776adaaacdc5c4e8ab0395120a6e972b, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 1.24, y: 1.1798275, z: 2.9983473}
    IKPositionWeight: 1
    root: {fileID: 7402654799015674516}
    target: {fileID: 0}
    spine:
    - transform: {fileID: 0}
      weight: 0
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: -0.10808878, y: -0.008514151, z: -6.103008e-15}
      defaultLocalRotation: {x: -2.1942628e-24, y: -1.6274388e-23, z: 0.06238862,
        w: 0.99805194}
      length: 0
      sqrMag: 0
      axis: {x: -0.12815899, y: -0.9917538, z: -0.00000014901161}
      baseForwardOffsetEuler: {x: 0, y: 0, z: 0}
    head:
      transform: {fileID: 0}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0, y: 0, z: 0}
      defaultLocalRotation: {x: 0, y: -0, z: -0, w: 1}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 1}
      baseForwardOffsetEuler: {x: 0, y: 0, z: 0}
    eyes: []
    bodyWeight: 0.7
    headWeight: 1
    eyesWeight: 0
    clampWeight: 0.7
    clampWeightHead: 0.5
    clampWeightEyes: 0
    clampSmoothing: 0
    spineWeightCurve:
      serializedVersion: 2
      m_Curve:
      - serializedVersion: 3
        time: 0
        value: 0.3
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      - serializedVersion: 3
        time: 1
        value: 1
        inSlope: 0
        outSlope: 0
        tangentMode: 0
        weightedMode: 0
        inWeight: 0
        outWeight: 0
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    spineTargetOffset: {x: 0, y: 0, z: 0}
--- !u!1 &1274186072419411901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1944894567241051680}
  m_Layer: 0
  m_Name: CC_Base_R_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1944894567241051680
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1274186072419411901}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5000003, y: -0.49999976, z: -0.5000003, w: -0.49999976}
  m_LocalPosition: {x: 0.062119015, y: 0.05875595, z: -0.032109845}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4490211272603021980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1359660982261028693
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7299991780694236420}
  m_Layer: 0
  m_Name: CC_Base_Teeth01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7299991780694236420
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1359660982261028693}
  serializedVersion: 2
  m_LocalRotation: {x: 1, y: 0.00023426232, z: 0.00003416539, w: 0.000011044115}
  m_LocalPosition: {x: -0.0010065723, y: 0.0037287902, z: -0.000025710386}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7234163297638815730}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1366070861005742818
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4149227583924101474}
  - component: {fileID: 327457730920912385}
  - component: {fileID: 4325181824298377204}
  - component: {fileID: 5546872762088274915}
  m_Layer: 0
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4149227583924101474
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1366070861005742818}
  serializedVersion: 2
  m_LocalRotation: {x: 0.12092967, y: 0.0028766154, z: 0.029973214, w: 0.9922043}
  m_LocalPosition: {x: 0.00000015258789, y: 0.10289588, z: 0.0000000047683715}
  m_LocalScale: {x: 0.99999976, y: 0.9999998, z: 0.9999997}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7669328701674395276}
  - {fileID: 3984984611843329466}
  m_Father: {fileID: 4605998933334557323}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &327457730920912385
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1366070861005742818}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &4325181824298377204
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1366070861005742818}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08439919
  m_Height: 0.30946368
  m_Direction: 1
  m_Center: {x: 0.0000002495945, y: 0.14066541, z: -0.0000000111758744}
--- !u!153 &5546872762088274915
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1366070861005742818}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6469044919349165650}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &1567970898528763514
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8731906559322667490}
  m_Layer: 0
  m_Name: ball_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8731906559322667490
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1567970898528763514}
  serializedVersion: 2
  m_LocalRotation: {x: 0.47116238, y: -0.020159548, z: 0.024802627, w: 0.8814672}
  m_LocalPosition: {x: -0.0000018787383, y: 0.14396666, z: 0.0000007390976}
  m_LocalScale: {x: 1, y: 0.9999998, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8169557302639763551}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1583472793903339293
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3304145376778902292}
  - component: {fileID: 1551054551495261239}
  - component: {fileID: 7599577786822137594}
  m_Layer: 8
  m_Name: CoverIndicator
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &3304145376778902292
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583472793903339293}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.7071068, z: -0.7071068, w: 0}
  m_LocalPosition: {x: 0, y: 0.858, z: 0.525}
  m_LocalScale: {x: 0.20951428, y: 1.373864, z: 0.23819567}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 90, y: 180, z: 0}
--- !u!33 &1551054551495261239
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583472793903339293}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7599577786822137594
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1583472793903339293}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 1450695b1dfa13f44b767188c630ffdd, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &1585014999227134831
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6961841487412310924}
  m_Layer: 0
  m_Name: root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6961841487412310924
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1585014999227134831}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0.0000029843027, y: 0, z: -0.03379729}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5928552661432004395}
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1626608604723851634
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2061383217299137637}
  m_Layer: 8
  m_Name: muzzle
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2061383217299137637
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1626608604723851634}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.051890533, z: 0.13181114}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1644352986344447460
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2813250818033119342}
  m_Layer: 8
  m_Name: AimingTransformHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2813250818033119342
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1644352986344447460}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11498396, y: -0.00049223204, z: 0.006160667, w: -0.9933481}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3624260035379648237}
  m_LocalEulerAnglesHint: {x: 100, y: 90, z: -180}
--- !u!1 &1759374672548104196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5078890076475787187}
  m_Layer: 0
  m_Name: pinky_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5078890076475787187
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759374672548104196}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0072141318, y: -0.000017000275, z: 0.04986973, w: 0.9987297}
  m_LocalPosition: {x: 0.00000030517577, y: 0.01617073, z: -0.00000029563904}
  m_LocalScale: {x: 1.0000002, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4053749872605063169}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1791347788396126279
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4797740664379296513}
  m_Layer: 0
  m_Name: ring_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4797740664379296513
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1791347788396126279}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07828313, y: 0.00031734316, z: -0.043870766, w: 0.99596536}
  m_LocalPosition: {x: 0.0020114135, y: 0.087606505, z: -0.015832042}
  m_LocalScale: {x: 1, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3717996316510139074}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1864070461529202637
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2195793653869371303}
  m_Layer: 8
  m_Name: Modules
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2195793653869371303
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1864070461529202637}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6822604474119824138}
  - {fileID: 4909386304134227756}
  - {fileID: 5344600829119329324}
  - {fileID: 7402654799015674516}
  - {fileID: 7247426993471718996}
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1978959605289735503
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5127146508601231717}
  - component: {fileID: 1926729403111026202}
  - component: {fileID: 8008859430569989912}
  m_Layer: 8
  m_Name: AimIK Before Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5127146508601231717
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978959605289735503}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2331077866999755286}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1926729403111026202
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978959605289735503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 4.8381405, y: 0.7027439, z: -0.28686422}
    IKPositionWeight: 0
    root: {fileID: 5127146508601231717}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 4777220526592169751}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 0}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: 2.197527, y: 0.8302772, z: 3.0314047}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!114 &8008859430569989912
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1978959605289735503}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: Only bones that used as muscle targets by PuppetMaster should ba added to
    AimIK's "Bones".
--- !u!1 &2026198142700098691
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5377096867207176154}
  - component: {fileID: 8956138468680928634}
  - component: {fileID: 6924136890832272134}
  - component: {fileID: 2494373269920450993}
  m_Layer: 8
  m_Name: AnimationStates
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5377096867207176154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2026198142700098691}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8956138468680928634
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2026198142700098691}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc8f0de9be674791873dab2408c742ef, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7400002, guid: 459a127509d869b40908dffa197da5fa, type: 3}
    - {fileID: 7400004, guid: e7468f78a01b86e439c96f57154dab25, type: 3}
    - {fileID: 7400000, guid: 2eca9b0c87f01c843903863e6d00a38d, type: 2}
    - {fileID: 7400000, guid: cb7af1a91c92e584697c05ddcffa8f85, type: 2}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 6
      Data: 
    - Name: <weaponsIdleAnimations>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[System.Collections.Generic.Dictionary`2[[WeaponSubState,
        General],[Animancer.ClipTransition, Animancer]], mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 7
      Data: 2|System.Collections.Generic.Dictionary`2[[WeaponSubState, General],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 3|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 3
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 2
    - Name: $v
      Entry: 7
      Data: 4|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 5|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 6|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 7|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 8|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 0
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 3
    - Name: $v
      Entry: 7
      Data: 9|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 10|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 11|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 12|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 13|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 1
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 5
    - Name: $v
      Entry: 7
      Data: 14|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 15|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 16|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 17|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 18|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 1
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 1
    - Name: $v
      Entry: 7
      Data: 19|System.Collections.Generic.Dictionary`2[[WeaponSubState, General],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 20|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 2
    - Name: $v
      Entry: 7
      Data: 21|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 22|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 23|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 24|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 25|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 2
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 3
    - Name: $v
      Entry: 7
      Data: 26|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 27|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 28|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 29|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 30|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 3
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _Character: {fileID: 3243832241670686071}
  _MainAnimation:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400002, guid: e5366f5926c8dd949a168f5f0095a4d3, type: 3}
    _Speed: 1
    _NormalizedStartTime: NaN
  _FirstRandomizeDelay: 5
  _MinRandomizeInterval: 0
  _MaxRandomizeInterval: 20
  _normalAnimations: []
--- !u!114 &6924136890832272134
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2026198142700098691}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 158ab36da7ed9dd449fd0093d07064d6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 6
      Data: 
    - Name: m_float1Controller
      Entry: 6
      Data: 
    - Name: <weaponsAnimations>k__BackingField
      Entry: 6
      Data: 
  _Character: {fileID: 3243832241670686071}
  rotationSpeed: 10
  vertical: 0
  horizontal: 0
  verticalDump: 0.25
  horizontalDump: 0.25
  inputAngle: 0
  inputMagnitude: 0
  inputMagnitudeDamp: 0.1
  InputAngleDump: 0
  InputAngleDumpA: 0.25
  InputAngleDumpB: 2
  InputAngleDumpT: 2
  m_debugParameterVector: {x: 0, y: 0}
  m_isMoving: 0
  m_fadeTime: 0.5
  m_NormalMovementAnimator: {fileID: 9100000, guid: 7122b14c8eac0a9488c25f928f30ec05,
    type: 2}
  m_normalWalkingBlendTree:
    StartWalkingBlendTree:
      _Asset: {fileID: 11400000, guid: f5a5b26c5309f3a448fdb4886f68c46a, type: 2}
    WalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 53da9a148d697da46bf5b1b4cbfa7291, type: 2}
  m_strufWalkingBlendTree:
    StartWalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 7714c8c08316bc044a7b8c38221ed939, type: 2}
    WalkingBlendTree:
      _Asset: {fileID: 11400000, guid: 73ff0b4602c1c9744979b3891ffef3ab, type: 2}
  m_weaponMovementMixers: []
  _canUpdate: 0
  m_footstepEventsAnimation: {fileID: 0}
  normalMovementResponseTime: 0.12
  aimingMovementResponseTime: 0.08
  startMovementBlendTime: 0.15
  directionChangeBlendTime: 0.2
  magnitudeChangeBlendTime: 0.1
  directionChangeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  footstepSpeedInfluence: 0.2
  _PlayFootstepAudio:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &2494373269920450993
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2026198142700098691}
  m_Enabled: 0
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 030557ec1fa0482e8e7af6244d95fd88, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 7400000, guid: f1a68184ce21e0d4fbce92973dc0363b, type: 2}
    - {fileID: 7400000, guid: 535f40ff3bb6b3349870fab03a5acfe4, type: 2}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: ConditionalAnimations
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.String, mscorlib],[Animancer.ClipTransition,
        Animancer]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.GenericEqualityComparer`1[[System.String,
        mscorlib]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 2
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AimToNormalLeft
    - Name: $v
      Entry: 7
      Data: 2|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 3|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 4|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 5|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 6|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 0
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 1
      Data: AimToNormalRight
    - Name: $v
      Entry: 7
      Data: 7|Animancer.ClipTransition, Animancer
    - Name: _FadeDuration
      Entry: 4
      Data: 0.25
    - Name: _Events
      Entry: 7
      Data: 8|Animancer.AnimancerEvent+Sequence+Serializable, Animancer
    - Name: _NormalizedTimes
      Entry: 7
      Data: 9|System.Single[], mscorlib
    - Name: 
      Entry: 14
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Callbacks
      Entry: 7
      Data: 10|UnityEngine.Events.UnityEvent[], UnityEngine.CoreModule
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Names
      Entry: 7
      Data: 11|System.String[], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _Clip
      Entry: 10
      Data: 1
    - Name: _Speed
      Entry: 4
      Data: 1
    - Name: _NormalizedStartTime
      Entry: 4
      Data: NaN
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  _Character: {fileID: 3243832241670686071}
  m_aimToNormal: 0
  _stopLeftBlendTreeAsset:
    _Asset: {fileID: 11400000, guid: 10359f3c59f7edd4db227abc226669b7, type: 2}
  _stopRightBlendTreeAsset:
    _Asset: {fileID: 11400000, guid: 82f9a2719f8c37d45a4ac17e833baa88, type: 2}
  vertical: 0
  horizontal: 0
  stopAnimationSpeedMultiplier: 2
  highSpeedStopMultiplier: 1.2
  aimingStopMultiplier: 0.9
  directionChangeInfluence: 0.3
  blendDuration: 0.15
--- !u!1 &2041860993112335076
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7391111005560746578}
  - component: {fileID: 1204976798827812914}
  m_Layer: 0
  m_Name: Ines_Default_Gloves
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7391111005560746578
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2041860993112335076}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1204976798827812914
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2041860993112335076}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b31a423ab50b8b3449f37b005177b217, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 279771734379647476, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 5572791610057042807}
  - {fileID: 3366948161025063600}
  - {fileID: 2753697593838296547}
  - {fileID: 1481966550462016051}
  - {fileID: 4053749872605063169}
  - {fileID: 8172053399810076699}
  - {fileID: 8683188798031981173}
  - {fileID: 6251702348690273879}
  - {fileID: 9209227907606372116}
  - {fileID: 7669328701674395276}
  - {fileID: 7360410177784678560}
  - {fileID: 1887670676407111013}
  - {fileID: 5113790155137111776}
  - {fileID: 4797740664379296513}
  - {fileID: 3717996316510139074}
  - {fileID: 2246782681655151770}
  - {fileID: 3662348311763307833}
  - {fileID: 7065130496213050685}
  - {fileID: 7133569396811251066}
  - {fileID: 6926629222984784688}
  - {fileID: 5211065926650188693}
  - {fileID: 7556803309533903411}
  - {fileID: 469256491857063785}
  - {fileID: 9220517768640357345}
  - {fileID: 5865011514679355812}
  - {fileID: 3765449231651182655}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 469256491857063785}
  m_AABB:
    m_Center: {x: 0.00601916, y: -0.42701393, z: 0.025629148}
    m_Extent: {x: 0.041894086, y: 0.81338686, z: 0.07448233}
  m_DirtyAABB: 0
--- !u!1 &2052239012528094405
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 292871324475126944}
  m_Layer: 8
  m_Name: MouseLookTarget
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &292871324475126944
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2052239012528094405}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 12.53}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8581761479138929668}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2075013282796105483
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4410766268693251201}
  - component: {fileID: 1970453879693082122}
  - component: {fileID: 1538990768306229589}
  - component: {fileID: 8166710278876192436}
  m_Layer: 9
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4410766268693251201
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075013282796105483}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00025808235, y: 0.00028837987, z: 0.99977154, w: -0.021374444}
  m_LocalPosition: {x: -0.09741045, y: -0.021480039, z: -0.00220668}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 119864837713895696}
  m_Father: {fileID: 1575208649713631053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1970453879693082122
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075013282796105483}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1538990768306229589
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075013282796105483}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.10784255
  m_Height: 0.52723026
  m_Direction: 1
  m_Center: {x: -0.0000002859161, y: 0.23964992, z: -0.00000034327965}
--- !u!153 &8166710278876192436
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2075013282796105483}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6615271875373956784}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2088079295391156365
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1887670676407111013}
  m_Layer: 0
  m_Name: index_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1887670676407111013
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2088079295391156365}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000016759956, y: -0.0053168475, z: 0.0070242267, w: 0.9999612}
  m_LocalPosition: {x: 0.00000015258789, y: 0.038484115, z: 0.00000030994414}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2061066770635408989}
  m_Father: {fileID: 6251702348690273879}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2107659490466025617
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6475188008645147268}
  - component: {fileID: 4016410368822921293}
  - component: {fileID: 7164650156448057384}
  - component: {fileID: 2689979412022799938}
  m_Layer: 9
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6475188008645147268
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2107659490466025617}
  serializedVersion: 2
  m_LocalRotation: {x: 0.037494026, y: 0.00000367303, z: 0.00011810292, w: 0.9992969}
  m_LocalPosition: {x: 0.000027410932, y: 0.106513344, z: 0.030964514}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6427481077866857254}
  - {fileID: 4099452736357410725}
  m_Father: {fileID: 1575208649713631053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &4016410368822921293
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2107659490466025617}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &7164650156448057384
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2107659490466025617}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.29151765, y: 0.43816063, z: 0.17491059}
  m_Center: {x: 0.00012554858, y: 0.19527864, z: -0.039149012}
--- !u!153 &2689979412022799938
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2107659490466025617}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6615271875373956784}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2166775307881434202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2898750158394801335}
  m_Layer: 8
  m_Name: GroundRaycaster5
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2898750158394801335
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2166775307881434202}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.0000001860467, z: 0, w: 1}
  m_LocalPosition: {x: 0.1877264, y: 0, z: -0.00000011395527}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3341596775319770571}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2336697626049121792
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5562929773770824881}
  m_Layer: 0
  m_Name: pinky_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5562929773770824881
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2336697626049121792}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0072581605, y: 0.000016972777, z: -0.05006522, w: 0.9987196}
  m_LocalPosition: {x: -0.00000015258789, y: 0.016174164, z: -0.000000057220458}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7065130496213050685}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2341144775645775812
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2246782681655151770}
  - component: {fileID: 7470339244645792554}
  - component: {fileID: 3647528435329587831}
  - component: {fileID: 8009930347372156690}
  m_Layer: 0
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2246782681655151770
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341144775645775812}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.00000015258789, y: 0.24901588, z: 0.0000001335144}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5211065926650188693}
  - {fileID: 7133569396811251066}
  - {fileID: 3662348311763307833}
  - {fileID: 4797740664379296513}
  - {fileID: 7556803309533903411}
  - {fileID: 3183515333007324220}
  m_Father: {fileID: 469256491857063785}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7470339244645792554
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341144775645775812}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &3647528435329587831
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341144775645775812}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07003571
  m_Height: 0.20543809
  m_Direction: 1
  m_Center: {x: -0.0000005289913, y: 0.09338101, z: -0.0000000018626454}
--- !u!153 &8009930347372156690
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2341144775645775812}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 305217397706512013}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2372966461723809106
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5211065926650188693}
  m_Layer: 0
  m_Name: index_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5211065926650188693
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2372966461723809106}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07187013, y: -0.0035265917, z: -0.040508132, w: 0.99658483}
  m_LocalPosition: {x: 0.0016203307, y: 0.09508155, z: 0.017892266}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5865011514679355812}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2377740911143651203
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6251702348690273879}
  m_Layer: 0
  m_Name: index_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6251702348690273879
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2377740911143651203}
  serializedVersion: 2
  m_LocalRotation: {x: -0.07301684, y: 0.0035579472, z: 0.040607445, w: 0.99649733}
  m_LocalPosition: {x: -0.0016212463, y: 0.095081024, z: 0.017892342}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1887670676407111013}
  m_Father: {fileID: 2753697593838296547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2380191110285251525
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3645407059040468473}
  m_Layer: 8
  m_Name: AimTransform
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3645407059040468473
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2380191110285251525}
  serializedVersion: 2
  m_LocalRotation: {x: -0.05011441, y: 0.6748035, z: -0.73562753, w: 0.031319}
  m_LocalPosition: {x: 0, y: 0.06, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9074641442115584318}
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: -108.337, y: -265.796, z: 272.1}
--- !u!1 &2407788004448289114
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2993532018169991005}
  - component: {fileID: 7966915517607231001}
  - component: {fileID: 1429689936800402161}
  - component: {fileID: 6508885152556466090}
  m_Layer: 0
  m_Name: thigh_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2993532018169991005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2407788004448289114}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0002580825, y: 0.00028840144, z: 0.9997715, w: -0.021374442}
  m_LocalPosition: {x: -0.09741045, y: -0.021480026, z: -0.0022066522}
  m_LocalScale: {x: 0.9999998, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9217853567996125564}
  - {fileID: 3484386517536547667}
  m_Father: {fileID: 5928552661432004395}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &7966915517607231001
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2407788004448289114}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1429689936800402161
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2407788004448289114}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.10784255
  m_Height: 0.52723026
  m_Direction: 1
  m_Center: {x: -0.0000002859161, y: 0.23964992, z: -0.00000034327965}
--- !u!153 &6508885152556466090
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2407788004448289114}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 63165348324869592}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &2526258472334098380
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2676361410354019826}
  - component: {fileID: 718323678659433593}
  m_Layer: 0
  m_Name: ToKo_Underwear_Bra_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &2676361410354019826
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2526258472334098380}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &718323678659433593
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2526258472334098380}
  m_Enabled: 0
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c985dd29bd7f42a4bbc1665cbb0d9943, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7698357121477753718, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 4712292338687573921}
  - {fileID: 2941098901988364380}
  - {fileID: 4303522748295484652}
  - {fileID: 4685813500312530625}
  - {fileID: 4605998933334557323}
  - {fileID: 7700676329067016366}
  - {fileID: 4149227583924101474}
  - {fileID: 196917263236161228}
  - {fileID: 3624260035379648237}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4303522748295484652}
  m_AABB:
    m_Center: {x: -0.0005009398, y: 0.26455414, z: -0.025684223}
    m_Extent: {x: 0.16582832, y: 0.18954834, z: 0.17397897}
  m_DirtyAABB: 0
--- !u!1 &2556885514211060709
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5497525267918870652}
  - component: {fileID: 6773687244174465998}
  - component: {fileID: 7913859413397816011}
  m_Layer: 8
  m_Name: QuadTextured
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5497525267918870652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556885514211060709}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7099972, y: 0, z: 0, w: 0.70420456}
  m_LocalPosition: {x: 0, y: 0.1, z: 0}
  m_LocalScale: {x: 0.5, y: 0.5, z: 0.5}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 547697443837435824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &6773687244174465998
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556885514211060709}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &7913859413397816011
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2556885514211060709}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 5f21e3dbd08bcea418f24a23a59f4cb3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &2580551076455878486
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5721097200813384516}
  m_Layer: 0
  m_Name: CC_Base_Tongue03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5721097200813384516
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2580551076455878486}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000046749043, y: -0.0010550104, z: 0.11461715, w: 0.9934092}
  m_LocalPosition: {x: -0.013736267, y: 0.000017242432, z: 0.00000092945993}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4657406374714801801}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2608357834273233052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4685813500312530625}
  m_Layer: 0
  m_Name: CC_Base_L_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4685813500312530625
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2608357834273233052}
  serializedVersion: 2
  m_LocalRotation: {x: -0.001101011, y: 0.6153887, z: 0.7882219, w: -0.0013287836}
  m_LocalPosition: {x: -0.098425545, y: 0.047770996, z: 0.14246082}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2941098901988364380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2639085821717869176
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4909386304134227756}
  - component: {fileID: 8684649439442927484}
  - component: {fileID: 3243832241670686071}
  m_Layer: 8
  m_Name: MovementModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4909386304134227756
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2639085821717869176}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2195793653869371303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8684649439442927484
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2639085821717869176}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 96268f2884c13d24eb4c6351a3388a7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  improvedMovement: 1
  useRootMotionForTurning: 0
  accelerationTime: 0.1
  decelerationTime: 0.2
  aimingSpeedMultiplier: 0.8
  strafingSpeedMultiplier: 0.9
  backpedalSpeedMultiplier: 0.7
  directionChangeCurve:
    serializedVersion: 2
    m_Curve:
    - serializedVersion: 3
      time: 0
      value: 0
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    - serializedVersion: 3
      time: 1
      value: 1
      inSlope: 0
      outSlope: 0
      tangentMode: 0
      weightedMode: 0
      inWeight: 0
      outWeight: 0
    m_PreInfinity: 2
    m_PostInfinity: 2
    m_RotationOrder: 4
  <ControllerIndex>k__BackingField: 0
  m_useAnimancer: 1
  <MainState>k__BackingField: 000000000100000002000000
  _moduleSubState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  animationModule: {fileID: 3243832241670686071}
  characterParameters: {fileID: 4766468187706380211}
  IsMoving: 0
  _subState: 0
  InputAngleDump: 0
  inputAngle: 0
  moveSpeed: 0.5
  rotationSpeed: 60
  m_targetRotationSpeed: 1
  m_minRotationAngle: 30
  m_useWholeBodyRotation: 1
  m_rotationSmoothTime: 0.2
  angleDifference: 0
  m_playerTransform: {fileID: 8775929964290693195}
  m_changeDirection: 0
  m_waitForChangeDirection: 0
  m_waitForStop: 0.5
  m_isTurningBack: 0
  _isTransitioning: 0
  _transitionCooldown: 0.25
  _improvedTransitionCooldown: 0.1
  fastRotationSpeed: 0.2
--- !u!114 &3243832241670686071
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2639085821717869176}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c977cd4b40934cb887c285ed1f8476e6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 8956138468680928634}
    - {fileID: 6924136890832272134}
    - {fileID: 2494373269920450993}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <States>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.Enum, mscorlib],[Module.Mono.Animancer.RealsticFemale.CharacterState,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.ObjectEqualityComparer`1[[System.Enum, mscorlib]],
        mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 4
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 2|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 3|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 4|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 1
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 5|MovementSubState, General
    - Name: 
      Entry: 3
      Data: 3
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 2
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_useAnimancer: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  _Animancer: {fileID: 9096211446228400863}
  _Parameters: {fileID: 4766468187706380211}
--- !u!1 &2674318801983123668
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4970681250608070657}
  m_Layer: 8
  m_Name: Mesh
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4970681250608070657
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2674318801983123668}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8754212100340269792}
  - {fileID: 2833792390761329531}
  - {fileID: 7934703814139299791}
  - {fileID: 7974120674929081353}
  - {fileID: 4621585381891678689}
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2783524720005150221
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 812914778976819066}
  - component: {fileID: 1665423961330587188}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &812914778976819066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2783524720005150221}
  serializedVersion: 2
  m_LocalRotation: {x: -0.86622465, y: 0.018037776, z: -0.010375641, w: 0.49922124}
  m_LocalPosition: {x: 0.0030535716, y: 0.22314279, z: 0.11494513}
  m_LocalScale: {x: 1.0000001, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1118397962452466628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &1665423961330587188
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2783524720005150221}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159393, y: 0.32100797, z: 0.12159393}
  m_Center: {x: 0.003245118, y: 0.17523567, z: -0.045210827}
--- !u!1 &2813510937857108022
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4690863144980786626}
  - component: {fileID: 3258125153270715800}
  m_Layer: 8
  m_Name: PistolAimingSubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4690863144980786626
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2813510937857108022}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5089675981995570609}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &3258125153270715800
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2813510937857108022}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 3ad99c1b3efc4acb90269d1b9eb71c1e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  AimingData:
    HasLeftHandIK: 1
    LeftHandFixTransform: {fileID: 9200543034088440229}
    AimTarget: {fileID: 3645407059040468473}
    Pointer: {fileID: 1307319351738626410}
--- !u!1 &2851433731252733492
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3925619699002202038}
  m_Layer: 0
  m_Name: thumb_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3925619699002202038
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2851433731252733492}
  serializedVersion: 2
  m_LocalRotation: {x: -0.045584954, y: -0.0017602224, z: -0.029903626, w: 0.99851125}
  m_LocalPosition: {x: -0, y: 0.026586989, z: 0.00000017166137}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7360410177784678560}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2941415199272447707
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4303522748295484652}
  - component: {fileID: 6692420677913717487}
  - component: {fileID: 6469044919349165650}
  - component: {fileID: 7445169510262059499}
  - component: {fileID: 4884057049452459654}
  m_Layer: 0
  m_Name: spine_02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4303522748295484652
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941415199272447707}
  serializedVersion: 2
  m_LocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738, w: 0.99217784}
  m_LocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
  m_LocalScale: {x: 1.0000001, y: 0.9999998, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2941098901988364380}
  m_Father: {fileID: 4777220526592169751}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6692420677913717487
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941415199272447707}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738, w: 0.99217784}
  limit: 40
  twistLimit: 40
--- !u!54 &6469044919349165650
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941415199272447707}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &7445169510262059499
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941415199272447707}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.29151765, y: 0.43816063, z: 0.17491059}
  m_Center: {x: 0.00012554858, y: 0.19527864, z: -0.039149012}
--- !u!153 &4884057049452459654
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2941415199272447707}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 63165348324869592}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3014244932486125052
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1067468565911564907}
  - component: {fileID: 4948108293576322970}
  m_Layer: 8
  m_Name: Behaviours
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1067468565911564907
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3014244932486125052}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7777472550933085132}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &4948108293576322970
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3014244932486125052}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 92471cc1733c49042be56cd39b4b6e5e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  text: All Puppet Behaviours should be parented to this GameObject, the PuppetMaster
    will automatically find them from here. All Puppet Behaviours have been designed
    so that they could be simply copied from one character to another without changing
    any references. It is important because they contain a lot of parameters and
    would be otherwise tedious to set up and tweak.
--- !u!1 &3057771152685439134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8373701522075401058}
  - component: {fileID: 8599886477154323762}
  m_Layer: 8
  m_Name: MP4State
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8373701522075401058
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3057771152685439134}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7247426993471718996}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &8599886477154323762
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3057771152685439134}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c4a16dc15d3946288a9c9963dfcc76d2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: m_OnEndAnimationEvent
      Entry: 6
      Data: 
  m_aimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  _Character: {fileID: 7811239351625802803}
  WeaponGameObject: {fileID: 0}
  Equip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  UnEquip:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  Idle:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: 2eca9b0c87f01c843903863e6d00a38d, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Aiming:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 7400000, guid: cb7af1a91c92e584697c05ddcffa8f85, type: 2}
    _Speed: 1
    _NormalizedStartTime: NaN
  Shooting:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
  Reloading:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
--- !u!1 &3061205873041183225
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 336202965966922876}
  - component: {fileID: 26260305857050672}
  - component: {fileID: 4451341245949035335}
  - component: {fileID: 53440755526576330}
  m_Layer: 9
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &336202965966922876
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3061205873041183225}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: -0.00000004121102, y: 0.24876305, z: -0.00000003562698}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1265225660549391993}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &26260305857050672
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3061205873041183225}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &4451341245949035335
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3061205873041183225}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.069964565
  m_Height: 0.20522939
  m_Direction: 1
  m_Center: {x: 0.00000040698797, y: 0.093286045, z: -0.000000031664964}
--- !u!153 &53440755526576330
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3061205873041183225}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5535407339797768801}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3067856066950680770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9128841205248196775}
  - component: {fileID: 1356207803689434135}
  m_Layer: 0
  m_Name: Sci_Fi_BGraph
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9128841205248196775
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3067856066950680770}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1356207803689434135
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3067856066950680770}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b1fe19bd086590f46a9d0c8ea7314681, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7392520633234987534, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 2941098901988364380}
  - {fileID: 196917263236161228}
  - {fileID: 7700676329067016366}
  - {fileID: 4712292338687573921}
  - {fileID: 4605998933334557323}
  - {fileID: 4149227583924101474}
  - {fileID: 3624260035379648237}
  - {fileID: 4303522748295484652}
  - {fileID: 4685813500312530625}
  - {fileID: 8683188798031981173}
  - {fileID: 7669328701674395276}
  - {fileID: 2753697593838296547}
  - {fileID: 6926629222984784688}
  - {fileID: 469256491857063785}
  - {fileID: 2246782681655151770}
  - {fileID: 3984984611843329466}
  - {fileID: 8025193364246868120}
  - {fileID: 4777220526592169751}
  - {fileID: 5928552661432004395}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 5928552661432004395}
  m_AABB:
    m_Center: {x: 0.0010021031, y: 0.3086796, z: 0.021864548}
    m_Extent: {x: 0.6461468, y: 0.24538122, z: 0.20483334}
  m_DirtyAABB: 0
--- !u!1 &3153611074146285353
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7781808719370263609}
  m_Layer: 0
  m_Name: thumb_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7781808719370263609
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3153611074146285353}
  serializedVersion: 2
  m_LocalRotation: {x: -0.045648206, y: 0.0017702165, z: 0.029804673, w: 0.9985113}
  m_LocalPosition: {x: -0.00000015258789, y: 0.026587524, z: 0.00000024318695}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9220517768640357345}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3174944581553429005
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1983295984226834791}
  m_Layer: 8
  m_Name: VaultCharRot_Tip
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1983295984226834791
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3174944581553429005}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 2.27}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7237376372815333097}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3228068840675992315
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9220517768640357345}
  m_Layer: 0
  m_Name: thumb_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9220517768640357345
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3228068840675992315}
  serializedVersion: 2
  m_LocalRotation: {x: -0.16171406, y: -0.004255523, z: 0.092745736, w: 0.98246056}
  m_LocalPosition: {x: -0.0000007629394, y: 0.05436756, z: -0.00000072479247}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7781808719370263609}
  m_Father: {fileID: 7556803309533903411}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3232837999061364273
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7133569396811251066}
  m_Layer: 0
  m_Name: middle_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7133569396811251066
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3232837999061364273}
  serializedVersion: 2
  m_LocalRotation: {x: -0.074639395, y: -0.0028822029, z: -0.0406939, w: 0.9963758}
  m_LocalPosition: {x: -0.00000030517577, y: 0.0929306, z: 0.000000019073486}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3765449231651182655}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3325956619048428499
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5258167489492070385}
  - component: {fileID: 7725524678241424973}
  m_Layer: 8
  m_Name: SphereDetectSensor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5258167489492070385
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3325956619048428499}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5344600829119329324}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7725524678241424973
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3325956619048428499}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b9ba30f9034691542b9ec15a5937cc06, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  LastPosition: {x: 0, y: 0, z: 0}
  m_collider: {fileID: 0}
  cooldownTime: 2
  canDetect: 1
  canLose: 1
  detectedObjects: []
  m_aimingModule: {fileID: 0}
  m_characterParameters: {fileID: 4766468187706380211}
--- !u!1 &3445855038414786982
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6822604474119824138}
  - component: {fileID: 2638255759238724534}
  m_Layer: 8
  m_Name: InputModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6822604474119824138
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3445855038414786982}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2195793653869371303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &2638255759238724534
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3445855038414786982}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: aaf09bfab896407fa91b5c272c57cb0d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 4766468187706380211}
    - {fileID: 7138319810809311850}
    - {fileID: 699025248306278272}
    - {fileID: 6617492742549939772}
    - {fileID: 1443667753020185120}
    - {fileID: 7811239351625802803}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <OutputValue>k__BackingField
      Entry: 6
      Data: 
    - Name: _needInputComponents
      Entry: 7
      Data: 0|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.INeedInput`1[[Module.Mono.Animancer.RealsticFemale.CharacterParameterEnum,
        General]], Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 6
    - Name: 
      Entry: 10
      Data: 0
    - Name: 
      Entry: 10
      Data: 1
    - Name: 
      Entry: 10
      Data: 2
    - Name: 
      Entry: 10
      Data: 3
    - Name: 
      Entry: 10
      Data: 4
    - Name: 
      Entry: 10
      Data: 5
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_playerTransform: {fileID: 2813250818033119342}
  inputAngleMultiplier: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  CharacterPositionCompass: {fileID: 197955182703635422}
  InputDirectionCompass: {fileID: 8393138137184261682}
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  <State>k__BackingField: 0
  rawHorizontalInput: 0
  rawVerticalInput: 0
  <horizontalInput>k__BackingField: 0
  <verticalInput>k__BackingField: 0
  <startInputAngle>k__BackingField: 0
  <inputAngle>k__BackingField: 0
  <stopInputAngle>k__BackingField: 0
  <inputMagnitude>k__BackingField: 0
  <runFactor>k__BackingField: 0
  <playerInputDirection>k__BackingField: {x: 0, y: 0, z: 0}
  m_isRunning: 0
  m_isLeftMoving: 0
  m_isRightMoving: 0
  m_isFwrdMoving: 0
  m_isBackMoving: 0
  m_isLeftFootstep: 0
  m_isRightFootstep: 0
  m_currentWeaponIndex: 0
  m_maxWeaponIndex: 1
  m_equipWeapon: 0
  m_unEquipWeapon: 0
  m_interact: 0
  EnableAutoAim: 1
  IsAiming: 0
  IsDetectTarget: 0
  EnableAutoShooting: 1
  IsShooting: 0
  LastShootingState: 0
--- !u!1 &3481404267767422758
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7669328701674395276}
  - component: {fileID: 8866247670178506157}
  - component: {fileID: 5251748393490456330}
  - component: {fileID: 7059923751833686048}
  m_Layer: 0
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7669328701674395276
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3481404267767422758}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048121444, y: -0.000012135147, z: -0.004907392, w: 0.99998796}
  m_LocalPosition: {x: -0.00000061035155, y: 0.2813307, z: -0.000000009536743}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2753697593838296547}
  - {fileID: 8683188798031981173}
  m_Father: {fileID: 4149227583924101474}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8866247670178506157
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3481404267767422758}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &5251748393490456330
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3481404267767422758}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075959265
  m_Height: 0.2736391
  m_Direction: 1
  m_Center: {x: 0.0000005689216, y: 0.12438155, z: -0.00000009420073}
--- !u!153 &7059923751833686048
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3481404267767422758}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 327457730920912385}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.5623221
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.43768
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3512130882336212954
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6691559850851508290}
  - component: {fileID: 4893069974307948550}
  - component: {fileID: 4195383630430310170}
  m_Layer: 8
  m_Name: Aim Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6691559850851508290
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3512130882336212954}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0.049999952, y: 1.09, z: 2.04}
  m_LocalScale: {x: 0.1, y: 0.1, z: 0.1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2331077866999755286}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &4893069974307948550
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3512130882336212954}
  m_Mesh: {fileID: 10207, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &4195383630430310170
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3512130882336212954}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: aae886dd0d5d59844b4ec40cc2d96918, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 1
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &3516186745044731717
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4490211272603021980}
  m_Layer: 0
  m_Name: CC_Base_FacialBone
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4490211272603021980
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3516186745044731717}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000017471124, y: -0.7071068, z: -0.0000017471124, w: 0.7071068}
  m_LocalPosition: {x: 0.0000000026170164, y: 0.00000030517577, z: 0.000000026226044}
  m_LocalScale: {x: 1, y: 1, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9130952369729836117}
  - {fileID: 3686526413400921503}
  - {fileID: 1944894567241051680}
  - {fileID: 7234163297638815730}
  m_Father: {fileID: 6187010263963786470}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3524643100510523141
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3341596775319770571}
  m_Layer: 8
  m_Name: GroundRaycaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3341596775319770571
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3524643100510523141}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.1, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4145270954867361742}
  - {fileID: 1479284470707564362}
  - {fileID: 4161593320679618462}
  - {fileID: 2898750158394801335}
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3527212283421332897
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3790389470756885847}
  m_Layer: 0
  m_Name: ring_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3790389470756885847
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3527212283421332897}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0008003152, y: -0.000099235, z: -0.023055706, w: 0.99973387}
  m_LocalPosition: {x: -0.0000015258788, y: 0.027640838, z: -0.0000021362305}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3717996316510139074}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3594500236130276970
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4621585381891678689}
  - component: {fileID: 783612253301890310}
  m_Layer: 8
  m_Name: trigger 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4621585381891678689
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3594500236130276970}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4970681250608070657}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &783612253301890310
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3594500236130276970}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8050392602797321999, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 5945311810865105750}
  - {fileID: 2381486250098498953}
  - {fileID: 2423114747879000155}
  - {fileID: 1243558745997792308}
  - {fileID: 7904709490601092112}
  - {fileID: 4495432798587655918}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2423114747879000155}
  m_AABB:
    m_Center: {x: 0, y: -0.052191965, z: 0.045141164}
    m_Extent: {x: 0.003946688, y: 0.0154629145, z: 0.010327298}
  m_DirtyAABB: 0
--- !u!1 &3605515943129080284
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8172053399810076699}
  m_Layer: 0
  m_Name: middle_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8172053399810076699
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3605515943129080284}
  serializedVersion: 2
  m_LocalRotation: {x: -0.075788125, y: 0.0029155503, z: 0.040822934, w: 0.99628365}
  m_LocalPosition: {x: -0, y: 0.09293022, z: 0.000000038146972}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5113790155137111776}
  m_Father: {fileID: 2753697593838296547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &3608576951237370773
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2753697593838296547}
  - component: {fileID: 1076315366755322759}
  - component: {fileID: 3742529860833449882}
  - component: {fileID: 1504874227460432074}
  m_Layer: 0
  m_Name: hand_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2753697593838296547
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3608576951237370773}
  serializedVersion: 2
  m_LocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
  m_LocalPosition: {x: 0.00000015258789, y: 0.24876311, z: -0.000000038146972}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6251702348690273879}
  - {fileID: 8172053399810076699}
  - {fileID: 1481966550462016051}
  - {fileID: 5572791610057042807}
  - {fileID: 9209227907606372116}
  m_Father: {fileID: 7669328701674395276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &1076315366755322759
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3608576951237370773}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &3742529860833449882
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3608576951237370773}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.069964565
  m_Height: 0.20522939
  m_Direction: 1
  m_Center: {x: 0.00000040698797, y: 0.093286045, z: -0.000000031664964}
--- !u!153 &1504874227460432074
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3608576951237370773}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8866247670178506157}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3859429190485955723
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1845394404451702405}
  - component: {fileID: 3820204614240438780}
  - component: {fileID: 4736531067074288933}
  m_Layer: 9
  m_Name: foot_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1845394404451702405
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3859429190485955723}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26213843, y: -0.07327937, z: 0.035943985, w: 0.9615725}
  m_LocalPosition: {x: -0.0000004209578, y: 0.49376276, z: -0.000000637956}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9039848297957138845}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3820204614240438780
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3859429190485955723}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &4736531067074288933
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3859429190485955723}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5166316140777864743}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &3987553576839538807
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8754212100340269792}
  - component: {fileID: 285811956216377388}
  - component: {fileID: 3069189906835084805}
  m_Layer: 8
  m_Name: body 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8754212100340269792
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3987553576839538807}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4970681250608070657}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &285811956216377388
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3987553576839538807}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -8545447256379345787, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 5945311810865105750}
  - {fileID: 2381486250098498953}
  - {fileID: 2423114747879000155}
  - {fileID: 1243558745997792308}
  - {fileID: 7904709490601092112}
  - {fileID: 4495432798587655918}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2423114747879000155}
  m_AABB:
    m_Center: {x: -0.00033851434, y: -0.07057169, z: 0.058352485}
    m_Extent: {x: 0.014922695, y: 0.07614103, z: 0.115497485}
  m_DirtyAABB: 0
--- !u!114 &3069189906835084805
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3987553576839538807}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8a054274092705d47b8cc27f27aab3bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  dataContainer:
    rid: 7234055931542373121
  references:
    version: 2
    RefIds:
    - rid: -2
      type: {class: , ns: , asm: }
    - rid: 7234055931542373121
      type: {class: DataContainer, ns: BrainFailProductions.PolyFew, asm: Assembly-CSharp}
      data:
        objectsHistory:
          rid: 7234055931542373122
        objectMeshPairs:
          rid: 7234055931542373125
        currentLodLevelSettings:
        - reductionStrength: 0
          transitionHeight: 0.6
          preserveUVFoldover: 0
          preserveUVSeams: 0
          preserveBorders: 0
          useEdgeSort: 1
          recalculateNormals: 0
          aggressiveness: 7
          maxIterations: 100
          regardCurvature: 0
          regardTolerance: 0
          combineMeshes: 0
          simplificationOptionsFoldout: 0
          intensityFoldout: 0
          clearBlendshapesComplete: 0
          generateUV2: 0
          sphereIntensities: []
        - reductionStrength: 30
          transitionHeight: 0.4
          preserveUVFoldover: 0
          preserveUVSeams: 0
          preserveBorders: 0
          useEdgeSort: 1
          recalculateNormals: 0
          aggressiveness: 7
          maxIterations: 100
          regardCurvature: 0
          regardTolerance: 0
          combineMeshes: 0
          simplificationOptionsFoldout: 0
          intensityFoldout: 0
          clearBlendshapesComplete: 0
          generateUV2: 0
          sphereIntensities: []
        - reductionStrength: 60
          transitionHeight: 0.15
          preserveUVFoldover: 0
          preserveUVSeams: 0
          preserveBorders: 0
          useEdgeSort: 1
          recalculateNormals: 0
          aggressiveness: 7
          maxIterations: 100
          regardCurvature: 0
          regardTolerance: 0
          combineMeshes: 0
          simplificationOptionsFoldout: 0
          intensityFoldout: 0
          clearBlendshapesComplete: 0
          generateUV2: 0
          sphereIntensities: []
        toleranceSpheres: []
        lodBackup:
          rid: -2
        textureArraysSettings:
          rid: 7234055931542373124
        materialsToRestore: []
        lastObjMaterialLinks: {fileID: 0}
        relocateMaterialLinks: 0
        reInitializeTempMatProps: 0
        choiceTextureMap: 0
        choiceDiffuseColorSpace: 0
        batchFewSavePath: 
        existingTextureArrays: []
        existingTextureArraysFoldout: 0
        existingTextureArraysSize: 0
        textureArraysPropsFoldout: 0
        existingArraysProps:
          rid: -2
        choiceColorSpace: 0
        preserveBorders: 0
        preserveUVSeams: 0
        preserveUVFoldover: 0
        useEdgeSort: 0
        recalculateNormals: 0
        maxIterations: 100
        aggressiveness: 7
        regardCurvature: 0
        considerChildren: 0
        isPreservationActive: 0
        sphereDiameter: 0.5
        oldSphereScale: {x: 0, y: 0, z: 0}
        reductionStrength: 0
        reductionPending: 0
        prevFeasibleTarget: {fileID: 0}
        runOnThreads: 0
        triangleCount: 194
        lastDrawer: {fileID: 0}
        foldoutAutoLOD: 0
        foldoutBatchFew: 0
        foldoutAutoLODMultiple: 0
        objPositionPrevFrame: {x: 0, y: 0, z: 0}
        objScalePrevFrame: {x: 1, y: 1, z: 1}
        considerChildrenBatchFew: 1
        autoLODSavePath: Assets/
        foldoutAdditionalOpts: 0
        generateUV2: 0
        copyParentStaticFlags: 0
        copyParentTag: 0
        copyParentLayer: 0
        createAsChildren: 0
        removeLODBackupComponent: 0
        generateUV2batchfew: 0
        removeMaterialLinksComponent: 0
        clearBlendshapesComplete: 0
        clearBlendshapesNormals: 0
        clearBlendshapesTangents: 0
        isPlainSkin: 0
    - rid: 7234055931542373122
      type: {class: DataContainer/UndoRedoOps, ns: BrainFailProductions.PolyFew, asm: Assembly-CSharp}
      data:
        gameObject: {fileID: 3987553576839538807}
        undoOperations:
        - isReduceDeep: 0
          objectMeshPairs:
            _Buckets: 00000000ffffffffffffffff
            _HashCodes: d6dfff7f0000000000000000
            _Next: ffffffff0000000000000000
            _Count: 1
            _Version: 1
            _FreeList: -1
            _FreeCount: 0
            _Keys:
            - {fileID: 3987553576839538807}
            - {fileID: 0}
            - {fileID: 0}
            _Values:
            - attachedToMeshFilter: 0
              mesh: {fileID: 0}
            - attachedToMeshFilter: 0
              mesh: {fileID: 0}
            - attachedToMeshFilter: 0
              mesh: {fileID: 0}
        redoOperations: []
    - rid: 7234055931542373124
      type: {class: CombiningInformation/TextureArrayGroup, ns: BrainFailProductions.PolyFew,
        asm: Assembly-CSharp}
      data:
        diffuseArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        metallicArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        specularArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        normalArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        heightArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        occlusionArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        emissiveArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        detailMaskArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        detailAlbedoArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
        detailNormalArraySettings:
          resolution:
            width: 512
            height: 512
          filteringMode: 0
          compressionType: 0
          compressionQuality: 1
          anisotropicFilteringLevel: 1
          choiceResolutionW: 4
          choiceResolutionH: 4
          choiceFilteringMode: 0
          choiceCompressionQuality: 1
          choiceCompressionType: 0
    - rid: 7234055931542373125
      type: {class: DataContainer/ObjectMeshPair, ns: BrainFailProductions.PolyFew,
        asm: Assembly-CSharp}
      data:
        _Buckets: 00000000ffffffffffffffff
        _HashCodes: d6dfff7f0000000000000000
        _Next: ffffffff0000000000000000
        _Count: 1
        _Version: 1
        _FreeList: -1
        _FreeCount: 0
        _Keys:
        - {fileID: 3987553576839538807}
        - {fileID: 0}
        - {fileID: 0}
        _Values:
        - attachedToMeshFilter: 0
          mesh: {fileID: 4300000, guid: 81b06d3925ebb4247bf93a87be8dded2, type: 2}
        - attachedToMeshFilter: 0
          mesh: {fileID: 0}
        - attachedToMeshFilter: 0
          mesh: {fileID: 0}
--- !u!1 &3995497635382727140
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9209227907606372116}
  m_Layer: 0
  m_Name: thumb_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9209227907606372116
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 3995497635382727140}
  serializedVersion: 2
  m_LocalRotation: {x: 0.30981874, y: 0.12231633, z: 0.09624521, w: 0.9379701}
  m_LocalPosition: {x: -0.008979035, y: 0.022824248, z: 0.018420162}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7360410177784678560}
  m_Father: {fileID: 2753697593838296547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4046379476385458724
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8327770814347762760}
  - component: {fileID: 7905871042871541643}
  - component: {fileID: 7848675147940948927}
  - component: {fileID: 5857706960222336820}
  m_Layer: 9
  m_Name: PuppetMaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8327770814347762760
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4046379476385458724}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5228426364522323745}
  - {fileID: 1575208649713631053}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7905871042871541643
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4046379476385458724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1c86ba130e5a5458a98e3b482192a6dd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  humanoidConfig: {fileID: 0}
  targetRoot: {fileID: 8775929964290693195}
  state: 0
  stateSettings:
    killDuration: 1
    deadMuscleWeight: 0.01
    deadMuscleDamper: 2
    maxFreezeSqrVelocity: 0.02
    freezePermanently: 0
    enableAngularLimitsOnKill: 1
    enableInternalCollisionsOnKill: 1
  mode: 2
  blendTime: 0.1
  fixTargetTransforms: 1
  solverIterationCount: 2
  visualizeTargetPose: 1
  mappingWeight: 1
  pinWeight: 1
  muscleWeight: 1
  muscleSpring: 100
  muscleDamper: 0
  pinPow: 4
  pinDistanceFalloff: 5
  angularPinning: 0
  updateJointAnchors: 1
  supportTranslationAnimation: 0
  angularLimits: 0
  internalCollisions: 0
  muscles:
  - name: pelvis
    joint: {fileID: 5609573648203050230}
    target: {fileID: 5928552661432004395}
    props:
      group: 0
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 0
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_l
    joint: {fileID: 8166710278876192436}
    target: {fileID: 2993532018169991005}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 1
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_l
    joint: {fileID: 3672128142939602080}
    target: {fileID: 9217853567996125564}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 2
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_l
    joint: {fileID: 6893551593844817198}
    target: {fileID: 8169557302639763551}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 3
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: thigh_r
    joint: {fileID: 884123513665906388}
    target: {fileID: 2009526134938540747}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 4
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: calf_r
    joint: {fileID: 6109271867980557725}
    target: {fileID: 6553216298185710679}
    props:
      group: 5
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 5
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: foot_r
    joint: {fileID: 4736531067074288933}
    target: {fileID: 1118397962452466628}
    props:
      group: 6
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 6
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: spine_02
    joint: {fileID: 2689979412022799938}
    target: {fileID: 4303522748295484652}
    props:
      group: 1
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 7
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_l
    joint: {fileID: 4150183783532615535}
    target: {fileID: 4149227583924101474}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 8
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_l
    joint: {fileID: 723129614767656655}
    target: {fileID: 7669328701674395276}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 9
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_l
    joint: {fileID: 53440755526576330}
    target: {fileID: 2753697593838296547}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 10
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: upperarm_r
    joint: {fileID: 4298143726018690048}
    target: {fileID: 196917263236161228}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 11
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: lowerarm_r
    joint: {fileID: 3686158052654258277}
    target: {fileID: 469256491857063785}
    props:
      group: 3
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 12
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  - name: hand_r
    joint: {fileID: 4055683562605038964}
    target: {fileID: 2246782681655151770}
    props:
      group: 4
      mappingWeight: 1
      pinWeight: 1
      muscleWeight: 1
      muscleDamper: 1
      internalCollisionIgnores:
        ignoreAll: 0
        muscles: []
        groups: 
      animatedTargetChildren: []
    parentIndexes: 
    childIndexes: 
    childFlags: 
    kinshipDegrees: 
    broadcaster: {fileID: 0}
    jointBreakBroadcaster: {fileID: 0}
    positionOffset: {x: 0, y: 0, z: 0}
    additionalPin: {fileID: 0}
    additionalPinTarget: {fileID: 0}
    additionalPinWeight: 0
    mappedVelocity: {x: 0, y: 0, z: 0}
    mappedAngularVelocity: {x: 0, y: 0, z: 0}
    isPropMuscle: 0
    index: 13
    ignoreTargetVelocity: 0
    targetMappedPosition: {x: 0, y: 0, z: 0}
    targetMappedRotation: {x: 0, y: 0, z: 0, w: 1}
    targetSampledPosition: {x: 0, y: 0, z: 0}
    targetSampledRotation: {x: 0, y: 0, z: 0, w: 1}
  propMuscles: []
  solvers: []
  mapDisconnectedMuscles: 1
  storeTargetMappedState: 1
--- !u!114 &7848675147940948927
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4046379476385458724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: df3575b0ce53b4b3ab6c036214365cc2, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  characterController: {fileID: 8775929964290693195}
  characterControllerLayer: 8
  ragdollLayer: 9
  ignoreCollisionWithCharacterController:
    serializedVersion: 2
    m_Bits: 256
  ignoreCollisionWithRagdoll:
    serializedVersion: 2
    m_Bits: 256
--- !u!114 &5857706960222336820
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4046379476385458724}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc1cfd8a0119540e8970180fc708afbd, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  kinematicCollidersUpdateLimit:
    puppetsPerFrame: 10
  freeUpdateLimit:
    puppetsPerFrame: 100
  fixedUpdateLimit:
    puppetsPerFrame: 100
  collisionStayMessages: 1
  collisionExitMessages: 1
  activePuppetCollisionThresholdMlp: 0
--- !u!1 &4062766492369617913
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1353865422219731623}
  - component: {fileID: 1946894845952989406}
  m_Layer: 0
  m_Name: Ines_Body_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1353865422219731623
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4062766492369617913}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1946894845952989406
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4062766492369617913}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: d5385bf88a622f643a7d80205f075435, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 4083910945696752190, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 9217853567996125564}
  - {fileID: 761683113489791214}
  - {fileID: 6553216298185710679}
  - {fileID: 5521858126448997138}
  - {fileID: 8169557302639763551}
  - {fileID: 1118397962452466628}
  - {fileID: 4149227583924101474}
  - {fileID: 3984984611843329466}
  - {fileID: 2941098901988364380}
  - {fileID: 4605998933334557323}
  - {fileID: 196917263236161228}
  - {fileID: 8025193364246868120}
  - {fileID: 7700676329067016366}
  - {fileID: 3484386517536547667}
  - {fileID: 2993532018169991005}
  - {fileID: 5928552661432004395}
  - {fileID: 1871936065825758296}
  - {fileID: 2009526134938540747}
  - {fileID: 7669328701674395276}
  - {fileID: 469256491857063785}
  - {fileID: 3624260035379648237}
  - {fileID: 6187010263963786470}
  - {fileID: 9130952369729836117}
  - {fileID: 4685813500312530625}
  - {fileID: 4303522748295484652}
  - {fileID: 4712292338687573921}
  - {fileID: 4777220526592169751}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 5928552661432004395}
  m_AABB:
    m_Center: {x: 0.0009224564, y: -0.20033985, z: 0.015904918}
    m_Extent: {x: 0.43948817, y: 0.78844863, z: 0.21551068}
  m_DirtyAABB: 0
--- !u!1 &4082322779782060125
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3899082658436753149}
  m_Layer: 8
  m_Name: FloorAngleRaycaster
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3899082658436753149
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4082322779782060125}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.531, z: 0.373}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4145289166428296738
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2310010653122767096}
  m_Layer: 0
  m_Name: ring_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2310010653122767096
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4145289166428296738}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00081478525, y: 0.00009944815, z: 0.022980655, w: 0.9997356}
  m_LocalPosition: {x: -0.00000045776366, y: 0.026329346, z: 0.000000038146972}
  m_LocalScale: {x: 0.9999999, y: 1, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3366948161025063600}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4148644922935665373
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2833792390761329531}
  - component: {fileID: 8980412762307758224}
  m_Layer: 8
  m_Name: hammer 1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2833792390761329531
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4148644922935665373}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4970681250608070657}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8980412762307758224
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4148644922935665373}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 0
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 8ba43d49c78a14143a100157ea593d55, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 5970359239379961709, guid: e97017e8ea32f7b4eb78cd918a58ca5a, type: 3}
  m_Bones:
  - {fileID: 5945311810865105750}
  - {fileID: 2381486250098498953}
  - {fileID: 2423114747879000155}
  - {fileID: 1243558745997792308}
  - {fileID: 7904709490601092112}
  - {fileID: 4495432798587655918}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 2423114747879000155}
  m_AABB:
    m_Center: {x: 0, y: -0.0062306616, z: -0.03359142}
    m_Extent: {x: 0.0032007988, y: 0.*********, z: 0.*********}
  m_DirtyAABB: 0
--- !u!1 &4237741904684768848
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3867368653293550154}
  m_Layer: 8
  m_Name: InputAxisFloatHELPER
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3867368653293550154
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4237741904684768848}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4292146042458132173
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6187010263963786470}
  - component: {fileID: 2313632891555151309}
  - component: {fileID: 8765266630879995438}
  - component: {fileID: 734053084773373561}
  m_Layer: 0
  m_Name: head
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6187010263963786470
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4292146042458132173}
  serializedVersion: 2
  m_LocalRotation: {x: -0.14982727, y: 0.00070315227, z: -0.0061363596, w: 0.98869294}
  m_LocalPosition: {x: -0.000000023841856, y: 0.071730494, z: 0.000000038146972}
  m_LocalScale: {x: 1.0000001, y: 0.9999996, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4490211272603021980}
  m_Father: {fileID: 3624260035379648237}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2313632891555151309
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4292146042458132173}
  serializedVersion: 5
  m_Mass: 5.4900002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &8765266630879995438
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4292146042458132173}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.14914049
  m_Height: 0.27342424
  m_Direction: 1
  m_Center: {x: 0.00015779176, y: 0.0654333, z: -0.014051051}
--- !u!153 &734053084773373561
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4292146042458132173}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6469044919349165650}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -30
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 30
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &4378843590597608864
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8775929964290693195}
  - component: {fileID: 2713062770034213158}
  - component: {fileID: 3329576716905353071}
  - component: {fileID: 565569736675380289}
  - component: {fileID: 6111165325453930749}
  - component: {fileID: 9096211446228400863}
  - component: {fileID: 8892626510212262266}
  - component: {fileID: 4228709757427334998}
  m_Layer: 8
  m_Name: Female1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8775929964290693195
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2195793653869371303}
  - {fileID: 4247056872505880032}
  - {fileID: 6990328291944331678}
  - {fileID: 1845745633568939544}
  - {fileID: 7001565842920156955}
  - {fileID: 1353865422219731623}
  - {fileID: 7644751161447464665}
  - {fileID: 7391111005560746578}
  - {fileID: 6744960708624715790}
  - {fileID: 6961841487412310924}
  - {fileID: 9128841205248196775}
  - {fileID: 2676361410354019826}
  - {fileID: 2331077866999755286}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!95 &2713062770034213158
Animator:
  serializedVersion: 7
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_Avatar: {fileID: 9000000, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Controller: {fileID: 0}
  m_CullingMode: 1
  m_UpdateMode: 0
  m_ApplyRootMotion: 1
  m_LinearVelocityBlending: 0
  m_StabilizeFeet: 0
  m_AnimatePhysics: 0
  m_WarningMessage: 
  m_HasTransformHierarchy: 1
  m_AllowConstantClipSamplingOptimization: 1
  m_KeepAnimatorStateOnDisable: 0
  m_WriteDefaultValuesOnDisable: 0
--- !u!135 &3329576716905353071
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 1.3668647
  m_Center: {x: 0, y: 0.84438926, z: 0}
--- !u!114 &565569736675380289
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d9832ef7d144a5a4ca35019d9c2edd7a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  crosshairEnable: 1
--- !u!114 &6111165325453930749
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: dc915763b98747f5abd5c196122525e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _audioSource: {fileID: 0}
  _FootstepEvents: {fileID: 0}
  _FootSources: []
  _baseAnimationSpeed: 1
  _currentSpeedMultiplier: 1
  OnLeftFootstepHit:
    m_PersistentCalls:
      m_Calls: []
  OnRightFootstepHit:
    m_PersistentCalls:
      m_Calls: []
--- !u!114 &9096211446228400863
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d6ad7b53f86f9da4da426b673c422513, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animator: {fileID: 2713062770034213158}
  _ActionOnDisable: 0
  _PlayAutomatically: 0
  _Animations: []
  _Controller:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Controller: {fileID: 0}
    _ActionsOnStop: 
--- !u!114 &8892626510212262266
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b5c3ee4d6bc22a7499986d03b341d4bc, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  playerTransform: {fileID: 8775929964290693195}
  rotationSpeed: 720
--- !u!114 &4228709757427334998
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4378843590597608864}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a70e525c82ce9413fa4d940ad7fcf1db, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  references:
    root: {fileID: 8775929964290693195}
    pelvis: {fileID: 5928552661432004395}
    leftThigh: {fileID: 2993532018169991005}
    leftCalf: {fileID: 9217853567996125564}
    leftFoot: {fileID: 8169557302639763551}
    rightThigh: {fileID: 2009526134938540747}
    rightCalf: {fileID: 6553216298185710679}
    rightFoot: {fileID: 1118397962452466628}
    leftUpperArm: {fileID: 4149227583924101474}
    leftForearm: {fileID: 7669328701674395276}
    leftHand: {fileID: 2753697593838296547}
    rightUpperArm: {fileID: 196917263236161228}
    rightForearm: {fileID: 469256491857063785}
    rightHand: {fileID: 2246782681655151770}
    head: {fileID: 6187010263963786470}
    spine:
    - {fileID: 4777220526592169751}
    - {fileID: 4303522748295484652}
    eyes: []
  solver:
    executedInEditor: 0
    IKPosition: {x: 0, y: 0, z: 0}
    IKPositionWeight: 1
    root: {fileID: 8775929964290693195}
    iterations: 4
    chain:
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 4303522748295484652}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 01000000020000000300000004000000
      childConstraints:
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 4149227583924101474}
        bone2: {fileID: 2009526134938540747}
      - pushElasticity: 0
        pullElasticity: 1
        bone1: {fileID: 196917263236161228}
        bone2: {fileID: 2993532018169991005}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 4149227583924101474}
        bone2: {fileID: 196917263236161228}
      - pushElasticity: 0
        pullElasticity: 0
        bone1: {fileID: 2993532018169991005}
        bone2: {fileID: 2009526134938540747}
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 4149227583924101474}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 7669328701674395276}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 2753697593838296547}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 196917263236161228}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 469256491857063785}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 2246782681655151770}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 2993532018169991005}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 9217853567996125564}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 8169557302639763551}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    - pin: 0
      pull: 1
      push: 0
      pushParent: 0
      reach: 0.1
      reachSmoothing: 1
      pushSmoothing: 1
      nodes:
      - transform: {fileID: 2009526134938540747}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 6553216298185710679}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      - transform: {fileID: 1118397962452466628}
        weight: 1
        solverPosition: {x: 0, y: 0, z: 0}
        solverRotation: {x: 0, y: 0, z: 0, w: 1}
        defaultLocalPosition: {x: 0, y: 0, z: 0}
        defaultLocalRotation: {x: 0, y: 0, z: 0, w: 0}
        length: 0
        effectorPositionWeight: 0
        effectorRotationWeight: 0
        offset: {x: 0, y: 0, z: 0}
      children: 
      childConstraints: []
      bendConstraint:
        bone1: {fileID: 0}
        bone2: {fileID: 0}
        bone3: {fileID: 0}
        bendGoal: {fileID: 0}
        direction: {x: 1, y: 0, z: 0}
        rotationOffset: {x: 0, y: 0, z: 0, w: 0}
        weight: 0
        defaultLocalDirection: {x: 0, y: 0, z: 0}
        defaultChildDirection: {x: 0, y: 0, z: 0}
    effectors:
    - bone: {fileID: 4303522748295484652}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones:
      - {fileID: 2993532018169991005}
      - {fileID: 2009526134938540747}
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 4149227583924101474}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 196917263236161228}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 2993532018169991005}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 2009526134938540747}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 0}
      planeBone2: {fileID: 0}
      planeBone3: {fileID: 0}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 2753697593838296547}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 4149227583924101474}
      planeBone2: {fileID: 196917263236161228}
      planeBone3: {fileID: 4303522748295484652}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 2246782681655151770}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 196917263236161228}
      planeBone2: {fileID: 4149227583924101474}
      planeBone3: {fileID: 4303522748295484652}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 8169557302639763551}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 2993532018169991005}
      planeBone2: {fileID: 2009526134938540747}
      planeBone3: {fileID: 4303522748295484652}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    - bone: {fileID: 1118397962452466628}
      target: {fileID: 0}
      positionWeight: 0
      rotationWeight: 0
      position: {x: 0, y: 0, z: 0}
      rotation: {x: 0, y: 0, z: 0, w: 1}
      positionOffset: {x: 0, y: 0, z: 0}
      effectChildNodes: 1
      maintainRelativePositionWeight: 0
      childBones: []
      planeBone1: {fileID: 2009526134938540747}
      planeBone2: {fileID: 2993532018169991005}
      planeBone3: {fileID: 4303522748295484652}
      planeRotationOffset: {x: 0, y: 0, z: 0, w: 1}
    spineMapping:
      spineBones:
      - {fileID: 5928552661432004395}
      - {fileID: 4777220526592169751}
      - {fileID: 4303522748295484652}
      leftUpperArmBone: {fileID: 4149227583924101474}
      rightUpperArmBone: {fileID: 196917263236161228}
      leftThighBone: {fileID: 2993532018169991005}
      rightThighBone: {fileID: 2009526134938540747}
      iterations: 3
      twistWeight: 1
    boneMappings:
    - bone: {fileID: 6187010263963786470}
      maintainRotationWeight: 0
    limbMappings:
    - parentBone: {fileID: 4605998933334557323}
      bone1: {fileID: 4149227583924101474}
      bone2: {fileID: 7669328701674395276}
      bone3: {fileID: 2753697593838296547}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 7700676329067016366}
      bone1: {fileID: 196917263236161228}
      bone2: {fileID: 469256491857063785}
      bone3: {fileID: 2246782681655151770}
      maintainRotationWeight: 0
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 2993532018169991005}
      bone2: {fileID: 9217853567996125564}
      bone3: {fileID: 8169557302639763551}
      maintainRotationWeight: 1
      weight: 1
    - parentBone: {fileID: 0}
      bone1: {fileID: 2009526134938540747}
      bone2: {fileID: 6553216298185710679}
      bone3: {fileID: 1118397962452466628}
      maintainRotationWeight: 1
      weight: 1
    FABRIKPass: 1
    rootNode: {fileID: 4303522748295484652}
    spineStiffness: 0.5
    pullBodyVertical: 0.5
    pullBodyHorizontal: 0
--- !u!1 &4396233581121180746
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 469256491857063785}
  - component: {fileID: 305217397706512013}
  - component: {fileID: 5261095351409419098}
  - component: {fileID: 1774925704495514785}
  m_Layer: 0
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &469256491857063785
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4396233581121180746}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016458846, y: 0.000018592604, z: 0.0073306826, w: 0.9999731}
  m_LocalPosition: {x: 0.0000007629394, y: 0.28141707, z: 0.0000009012222}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2246782681655151770}
  - {fileID: 6926629222984784688}
  m_Father: {fileID: 196917263236161228}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &305217397706512013
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4396233581121180746}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &5261095351409419098
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4396233581121180746}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075982586
  m_Height: 0.27391738
  m_Direction: 1
  m_Center: {x: -0.00000032421664, y: 0.12450803, z: 0.0000000035406056}
--- !u!153 &1774925704495514785
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4396233581121180746}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 5562249760951509835}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.8402332
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.15976
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &4656581976527382777
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6926629222984784688}
  m_Layer: 0
  m_Name: lowerarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6926629222984784688
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4656581976527382777}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020659559, y: -0.0000015906267, z: 0.00000052282104,
    w: 1}
  m_LocalPosition: {x: -0, y: 0.12450798, z: 0.00000007152557}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 469256491857063785}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4671087073851677134
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4657406374714801801}
  m_Layer: 0
  m_Name: CC_Base_Tongue02
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4657406374714801801
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4671087073851677134}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0011598348, y: -0.005633796, z: -0.09942047, w: 0.9950289}
  m_LocalPosition: {x: -0.009785385, y: 0.0002241516, z: -0.0000043535233}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999998}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5721097200813384516}
  m_Father: {fileID: 3180524282443571956}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4696819815807595620
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7644751161447464665}
  - component: {fileID: 5507249491175160518}
  m_Layer: 0
  m_Name: Ines_Boots_V1
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7644751161447464665
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4696819815807595620}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5507249491175160518
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4696819815807595620}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 4def956d3fd555c4b835ed61252e1048, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2213655293627143353, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 8169557302639763551}
  - {fileID: 761683113489791214}
  - {fileID: 9217853567996125564}
  - {fileID: 1118397962452466628}
  - {fileID: 5521858126448997138}
  - {fileID: 6553216298185710679}
  - {fileID: 8731906559322667490}
  - {fileID: 4056800010831387403}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 6553216298185710679}
  m_AABB:
    m_Center: {x: 0.07914308, y: 0.54150337, z: 0.0098627955}
    m_Extent: {x: 0.1488527, y: 0.14436929, z: 0.14782879}
  m_DirtyAABB: 0
--- !u!1 &4731924669188330824
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1845745633568939544}
  - component: {fileID: 8811985765097846473}
  m_Layer: 0
  m_Name: CC_Base_Teeth
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &1845745633568939544
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4731924669188330824}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &8811985765097846473
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4731924669188330824}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 839c67f3e704bf8459f3e64929778661, type: 2}
  - {fileID: 2100000, guid: adc7f5c53274a7e4ca68c1466bd9a1ff, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -5727283436533813174, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 394745373956028053}
  - {fileID: 7299991780694236420}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 7299991780694236420}
  m_AABB:
    m_Center: {x: -0.001902123, y: -0.018704455, z: 0.000000009313226}
    m_Extent: {x: 0.024316978, y: 0.01927523, z: 0.023962753}
  m_DirtyAABB: 0
--- !u!1 &4740115244590228725
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5706714760049950198}
  m_Layer: 8
  m_Name: RaycasterFront
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5706714760049950198
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4740115244590228725}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.459, z: 0.189}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4742424289758940585
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5466703835676033078}
  m_Layer: 8
  m_Name: CompassArrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5466703835676033078
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4742424289758940585}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 1.2788713}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2332395235700116694}
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4742737860896582242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5572791610057042807}
  m_Layer: 0
  m_Name: ring_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5572791610057042807
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4742737860896582242}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0840788, y: -0.0007057925, z: 0.03980939, w: 0.99566334}
  m_LocalPosition: {x: -0.0015324402, y: 0.088897474, z: -0.016537819}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3366948161025063600}
  m_Father: {fileID: 2753697593838296547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4773764920941521946
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1307319351738626410}
  m_Layer: 8
  m_Name: Pointer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1307319351738626410
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4773764920941521946}
  serializedVersion: 2
  m_LocalRotation: {x: 0.61545503, y: -0.47431195, z: -0.5169701, w: -0.35914516}
  m_LocalPosition: {x: 0.007, y: 0.045, z: 0.112}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4822215346418773745
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8651355727699331694}
  - component: {fileID: 1361117614135083017}
  - component: {fileID: 3950926123071566574}
  m_Layer: 8
  m_Name: WallDetector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8651355727699331694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822215346418773745}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.301, z: 0.568}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &1361117614135083017
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822215346418773745}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.38
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &3950926123071566574
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4822215346418773745}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &4841377980894393500
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8581761479138929668}
  m_Layer: 8
  m_Name: MouseLookCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8581761479138929668
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4841377980894393500}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 1.303585, z: 0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 292871324475126944}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &4861916339775483442
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7024792088366721414}
  - component: {fileID: 8941871143686292579}
  - component: {fileID: 6003668221283175685}
  m_Layer: 8
  m_Name: VaultDetector
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7024792088366721414
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4861916339775483442}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.416, z: 0.574}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &8941871143686292579
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4861916339775483442}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.11
  m_Center: {x: 0, y: 0, z: 0}
--- !u!54 &6003668221283175685
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4861916339775483442}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!1 &4976514991349133150
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 761683113489791214}
  m_Layer: 0
  m_Name: calf_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &761683113489791214
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 4976514991349133150}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000035669185, y: -0.000019150324, z: -0.000027639298, w: 1}
  m_LocalPosition: {x: 0.00000021934508, y: 0.24685065, z: -0.00000035762787}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9217853567996125564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5221514395232740210
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3662348311763307833}
  m_Layer: 0
  m_Name: pinky_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3662348311763307833
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5221514395232740210}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08363916, y: -0.00074685307, z: -0.057137705, w: 0.9948564}
  m_LocalPosition: {x: 0.005911102, y: 0.08264572, z: -0.030138053}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7065130496213050685}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5301136031638475272
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3366948161025063600}
  m_Layer: 0
  m_Name: ring_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3366948161025063600
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5301136031638475272}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0009503501, y: 0.00013274279, z: -0.000009626135, w: 0.9999996}
  m_LocalPosition: {x: 0.00000061035155, y: 0.04171959, z: -0.00000071525574}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 2310010653122767096}
  m_Father: {fileID: 5572791610057042807}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5381963987330325932
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7065130496213050685}
  m_Layer: 0
  m_Name: pinky_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7065130496213050685
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5381963987330325932}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0070990967, y: -0.0012974498, z: -0.000019510031, w: 0.99997395}
  m_LocalPosition: {x: 0.00000015258789, y: 0.033094022, z: -0.000000085830685}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5562929773770824881}
  m_Father: {fileID: 3662348311763307833}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5534071422545848416
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 188281107476723005}
  - component: {fileID: 6535798778689592834}
  m_Layer: 8
  m_Name: LimbIK Left Hand
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &188281107476723005
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5534071422545848416}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2331077866999755286}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6535798778689592834
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5534071422545848416}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 4db3c450680fd4c809d5ad90a2f24e5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: -0.67545736, y: 1.582675, z: -0.06562546}
    IKPositionWeight: 0
    root: {fileID: 188281107476723005}
    target: {fileID: 0}
    IKRotationWeight: 0
    IKRotation: {x: 0.06974718, y: 0.07264444, z: 0.68527913, w: 0.72128403}
    bendNormal: {x: -0.00000012719387, y: -0.000000040853752, z: -0.0006869835}
    bone1:
      transform: {fileID: 4149227583924101474}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.10289588, z: 0.0000000047683715}
      defaultLocalRotation: {x: 0.12092967, y: 0.0028766154, z: 0.029973214, w: 0.9922043}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone2:
      transform: {fileID: 7669328701674395276}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: -0.00000061035155, y: 0.2813307, z: -0.000000009536743}
      defaultLocalRotation: {x: -0.00000048121444, y: -0.000012135147, z: -0.004907392,
        w: 0.99998796}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    bone3:
      transform: {fileID: 2753697593838296547}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 1}
      defaultLocalPosition: {x: 0.00000015258789, y: 0.24876311, z: -0.000000038146972}
      defaultLocalRotation: {x: 0.10059828, y: 0.001801374, z: -0.0280344, w: 0.99453044}
      length: 0
      sqrMag: 0
      axis: {x: -1, y: -0, z: -0}
    goal: 2
    bendModifier: 0
    maintainRotationWeight: 0
    bendModifierWeight: 1
    bendGoal: {fileID: 0}
--- !u!1 &5538277976900314912
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 744890345733490932}
  - component: {fileID: 3822340531467335120}
  - component: {fileID: 959717957060031635}
  m_Layer: 8
  m_Name: PlayerTrigger
  m_TagString: PlayerTrigger
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &744890345733490932
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5538277976900314912}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3822340531467335120
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5538277976900314912}
  serializedVersion: 5
  m_Mass: 1
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 0
  m_IsKinematic: 1
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!135 &959717957060031635
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5538277976900314912}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 0.32
  m_Center: {x: 0, y: 0.25, z: 0}
--- !u!1 &5619612385841202268
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2423114747879000155}
  m_Layer: 8
  m_Name: slide
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2423114747879000155
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5619612385841202268}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.053969655, z: -0.04202029}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1243558745997792308}
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5625442241186237830
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2941098901988364380}
  - component: {fileID: 1743149120580476802}
  m_Layer: 0
  m_Name: spine_03
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2941098901988364380
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5625442241186237830}
  serializedVersion: 2
  m_LocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701, w: 0.9872234}
  m_LocalPosition: {x: -7.8231094e-10, y: 0.12466316, z: 0.0000017356872}
  m_LocalScale: {x: 0.99999994, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4685813500312530625}
  - {fileID: 4712292338687573921}
  - {fileID: 4605998933334557323}
  - {fileID: 7700676329067016366}
  - {fileID: 3624260035379648237}
  m_Father: {fileID: 4303522748295484652}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1743149120580476802
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5625442241186237830}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: -0.15933207, y: -0.00021606042, z: -0.0018017701, w: 0.9872234}
  limit: 40
  twistLimit: 40
--- !u!1 &5655635055349212713
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1243558745997792308}
  m_Layer: 8
  m_Name: ejection
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1243558745997792308
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5655635055349212713}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: -0.00069135264, z: 0.058437884}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2423114747879000155}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &5895789578726093721
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7777472550933085132}
  - component: {fileID: 5318503382826552318}
  m_Layer: 8
  m_Name: GameObject
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7777472550933085132
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5895789578726093721}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1067468565911564907}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5318503382826552318
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 5895789578726093721}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1efacf79ab4214e85aeebfd07064c20f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 0}
  deactivated: 0
  masterProps:
    normalMode: 0
    mappingBlendSpeed: 10
    activateOnStaticCollisions: 0
    activateOnImpulse: 0
  groundLayers:
    serializedVersion: 2
    m_Bits: 2048
  collisionLayers:
    serializedVersion: 2
    m_Bits: 1
  collisionThreshold: 0
  collisionResistance:
    mode: 0
    floatValue: 25
    curve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
    tooltip: Smaller value means more unpinning from collisions so the characters
      get knocked out more easily. If using a curve, the value will be evaluated
      by each muscle's target velocity magnitude. This can be used to make collision
      resistance higher while the character moves or animates faster.
  collisionResistanceMultipliers: []
  maxCollisions: 1
  regainPinSpeed: 1.5
  boostFalloff: 0.1
  defaults:
    unpinParents: 1
    unpinChildren: 1
    unpinGroup: 0
    minMappingWeight: 0
    maxMappingWeight: 1
    minPinWeight: 0
    disableColliders: 0
    regainPinSpeed: 0.1
    collisionResistance: 1
    knockOutDistance: 0.7
    puppetMaterial: {fileID: 13400000, guid: a3fd0ffcf0d0a384ba3568687a96ab8e, type: 2}
    unpinnedMaterial: {fileID: 13400000, guid: d4563de398e2c4412a95f4317ddf4b1a, type: 2}
  groupOverrides:
  - name: Head
    groups: 02000000
    props:
      unpinParents: 1
      unpinChildren: 1
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.3
      collisionResistance: 1
      knockOutDistance: 0.4
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Arm, Hand, Prop
    groups: 030000000400000008000000
    props:
      unpinParents: 1
      unpinChildren: 1
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.3
      collisionResistance: 1
      knockOutDistance: 100
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Hips, Leg
    groups: 0000000005000000
    props:
      unpinParents: 0.9
      unpinChildren: 0.9
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 0.6
      collisionResistance: 1
      knockOutDistance: 0.4
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  - name: Foot
    groups: 06000000
    props:
      unpinParents: 0.7
      unpinChildren: 0
      unpinGroup: 0
      minMappingWeight: 0
      maxMappingWeight: 1
      minPinWeight: 0
      disableColliders: 0
      regainPinSpeed: 1
      collisionResistance: 1
      knockOutDistance: 0.5
      puppetMaterial: {fileID: 0}
      unpinnedMaterial: {fileID: 0}
  knockOutDistance: 5
  unpinnedMuscleWeightMlp: 1
  maxRigidbodyVelocity: 1
  pinWeightThreshold: 1
  unpinnedMuscleKnockout: 1
  dropProps: 0
  canGetUp: 1
  getUpDelay: 1
  blendToAnimationTime: 0.3
  maxGetUpVelocity: 0.4
  minGetUpDuration: 2
  getUpCollisionResistanceMlp: 4
  getUpRegainPinSpeedMlp: 3
  getUpKnockOutDistanceMlp: 20
  getUpOffsetProne: {x: 0, y: 0, z: 0}
  getUpOffsetSupine: {x: 0, y: 0, z: 0}
  isQuadruped: 0
  onGetUpProne:
    switchToBehaviour: 
    animations:
    - animationState: GetUpProne
      crossfadeTime: 0.2
      layer: 0
      resetNormalizedTime: 1
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onGetUpSupine:
    switchToBehaviour: 
    animations:
    - animationState: GetUpSupine
      crossfadeTime: 0.2
      layer: 0
      resetNormalizedTime: 1
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalance:
    switchToBehaviour: 
    animations:
    - animationState: Fall
      crossfadeTime: 0.7
      layer: 0
      resetNormalizedTime: 0
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalanceFromPuppet:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onLoseBalanceFromGetUp:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  onRegainBalance:
    switchToBehaviour: 
    animations: []
    unityEvent:
      m_PersistentCalls:
        m_Calls: []
  canMoveTarget: 1
--- !u!1 &6003510938767317470
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1481966550462016051}
  m_Layer: 0
  m_Name: pinky_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1481966550462016051
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6003510938767317470}
  serializedVersion: 2
  m_LocalRotation: {x: -0.08479613, y: 0.00079936325, z: 0.05727491, w: 0.9947505}
  m_LocalPosition: {x: -0.005909271, y: 0.0826802, z: -0.030114098}
  m_LocalScale: {x: 1, y: 0.9999999, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4053749872605063169}
  m_Father: {fileID: 2753697593838296547}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6009950951050118614
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4612088608534634171}
  - component: {fileID: 4524048340674016325}
  - component: {fileID: 6893551593844817198}
  m_Layer: 9
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4612088608534634171
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6009950951050118614}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: 0.00000045076, y: 0.49370146, z: -0.00000059790904}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 119864837713895696}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &4524048340674016325
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6009950951050118614}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &6893551593844817198
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6009950951050118614}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 2863726569885860666}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6131503234274281074
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6667973866451987449}
  - component: {fileID: 6863889055913149302}
  m_Layer: 8
  m_Name: AimIK After Physics
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6667973866451987449
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6131503234274281074}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2331077866999755286}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6863889055913149302
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6131503234274281074}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 5013856973b27429d937d256dc082f2e, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  fixTransforms: 1
  solver:
    executedInEditor: 0
    IKPosition: {x: 4.8381405, y: 0.7027439, z: -0.28686422}
    IKPositionWeight: 0
    root: {fileID: 6667973866451987449}
    target: {fileID: 0}
    tolerance: 0
    maxIterations: 4
    useRotationLimits: 1
    XY: 0
    bones:
    - transform: {fileID: 4777220526592169751}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.000016985006, y: 0.07044197, z: 0.018796988}
      defaultLocalRotation: {x: 0.16194537, y: 0.000021887443, z: -0.00013517268,
        w: 0.9867998}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 4303522748295484652}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 3.352761e-10, y: 0.038068235, z: 0.00000034332274}
      defaultLocalRotation: {x: -0.12483239, y: 0.000005946884, z: 0.00025184738,
        w: 0.99217784}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    - transform: {fileID: 3624260035379648237}
      weight: 1
      solverPosition: {x: 0, y: 0, z: 0}
      solverRotation: {x: 0, y: 0, z: 0, w: 0}
      defaultLocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
      defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
      length: 0
      sqrMag: 0
      axis: {x: 0, y: 0, z: 0}
    transform: {fileID: 0}
    axis: {x: 0, y: -1, z: 0}
    poleAxis: {x: 0, y: 0, z: 1}
    polePosition: {x: 2.197527, y: 0.8302772, z: 3.0314047}
    poleWeight: 0
    poleTarget: {fileID: 0}
    clampWeight: 0.5
    clampSmoothing: 2
--- !u!1 &6214097462487859889
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8169557302639763551}
  - component: {fileID: 5576076586640442881}
  - component: {fileID: 2468763960883606896}
  m_Layer: 0
  m_Name: foot_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8169557302639763551
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6214097462487859889}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26242954, y: 0.072389625, z: -0.036033686, w: 0.96155715}
  m_LocalPosition: {x: 0.00000044345856, y: 0.49370125, z: -0.00000071048737}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8731906559322667490}
  - {fileID: 5030884785618636209}
  m_Father: {fileID: 9217853567996125564}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5576076586640442881
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6214097462487859889}
  serializedVersion: 5
  m_Mass: 1.0875
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!153 &2468763960883606896
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6214097462487859889}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 2320732454331389162}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6221376846491228192
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2061066770635408989}
  m_Layer: 0
  m_Name: index_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2061066770635408989
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6221376846491228192}
  serializedVersion: 2
  m_LocalRotation: {x: -0.015389408, y: 0.000277427, z: 0.005215452, w: 0.999868}
  m_LocalPosition: {x: -0.0004373169, y: 0.028221816, z: -0.00072290417}
  m_LocalScale: {x: 1.0000004, y: 1.0000004, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1887670676407111013}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6223661188674897316
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5030884785618636209}
  - component: {fileID: 7593068584302796879}
  m_Layer: 0
  m_Name: Foot Collider
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5030884785618636209
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6223661188674897316}
  serializedVersion: 2
  m_LocalRotation: {x: -0.8662113, y: -0.017392445, z: 0.0100171575, w: 0.49927476}
  m_LocalPosition: {x: -0.0029380922, y: 0.2230567, z: 0.11497645}
  m_LocalScale: {x: 1.0000001, y: 0.99999976, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8169557302639763551}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!65 &7593068584302796879
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6223661188674897316}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.12159323, y: 0.32100618, z: 0.12159323}
  m_Center: {x: -0.0031298604, y: 0.17521249, z: -0.045183763}
--- !u!1 &6327300431395141035
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6159669958262507441}
  - component: {fileID: 4337086440014037475}
  - component: {fileID: 6960434135975977498}
  m_Layer: 8
  m_Name: Laser2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6159669958262507441
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6327300431395141035}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5277583, y: -0.5276576, z: -0.47133735, w: 0.46998906}
  m_LocalPosition: {x: -5.0121813, y: 0.025091648, z: -0.58743}
  m_LocalScale: {x: 0.0010000007, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 9074641442115584318}
  m_LocalEulerAnglesHint: {x: -0.076, y: -96.54, z: -90.079}
--- !u!33 &4337086440014037475
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6327300431395141035}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &6960434135975977498
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6327300431395141035}
  m_Enabled: 1
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 440d6a9870aca7b4d94eebb649da8773, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &6406722201058937949
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4066937904800744613}
  m_Layer: 8
  m_Name: CameraAutoLookAt
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4066937904800744613
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6406722201058937949}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0.95216703, z: 4.6503477}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 547697443837435824}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6406834782533140737
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3183515333007324220}
  - component: {fileID: 407324655579810657}
  m_Layer: 8
  m_Name: Pistol
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3183515333007324220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6406834782533140737}
  serializedVersion: 2
  m_LocalRotation: {x: -0.47546354, y: -0.49956912, z: -0.4298219, w: 0.5827679}
  m_LocalPosition: {x: -0.031, y: -0.015, z: 0.029}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 306422539164200583}
  m_Father: {fileID: 2246782681655151770}
  m_LocalEulerAnglesHint: {x: -79.616, y: -74.315, z: -8.268}
--- !u!114 &407324655579810657
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6406834782533140737}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 6a0b21ec855a7db42ab30d4665de8769, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  <CurrentAmmo>k__BackingField: 0
  <FireRate>k__BackingField: 0
  <NextFireTime>k__BackingField: 0
  <DamagePerShoot>k__BackingField: 0
  <Health>k__BackingField: 0
  <CanAim>k__BackingField: 0
  <CanReload>k__BackingField: 0
  <IsAiming>k__BackingField: 0
  <IsShooting>k__BackingField: 0
  <IsReloading>k__BackingField: 0
  <AimPointer>k__BackingField: {fileID: 8475396748067272981}
  <Accuracy>k__BackingField: 0.8
  <MaxRange>k__BackingField: 50
  <DamageAmount>k__BackingField: 25
  <SpreadAngle>k__BackingField: 5
  <RecoilAmount>k__BackingField: 1
  <RecoilRecovery>k__BackingField: 0.5
  <CurrentRecoil>k__BackingField: 0
  <LastShotTime>k__BackingField: 0
--- !u!1 &6476977039825213901
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4056800010831387403}
  m_Layer: 0
  m_Name: ball_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4056800010831387403
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6476977039825213901}
  serializedVersion: 2
  m_LocalRotation: {x: 0.47113988, y: 0.020058213, z: -0.025840553, w: 0.8814517}
  m_LocalPosition: {x: 0.0000048828124, y: 0.14396666, z: 0.0000016212463}
  m_LocalScale: {x: 0.9999997, y: 0.99999964, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 1118397962452466628}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6510654238865308594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6744960708624715790}
  - component: {fileID: 4338256183862170012}
  m_Layer: 0
  m_Name: Middle_Ponytail
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6744960708624715790
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6510654238865308594}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &4338256183862170012
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6510654238865308594}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: cac661ce02539ee40bd0c7acfcc4d0c7, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1155122597573229543, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 4303522748295484652}
  - {fileID: 2941098901988364380}
  - {fileID: 3624260035379648237}
  - {fileID: 6187010263963786470}
  - {fileID: 4605998933334557323}
  - {fileID: 4685813500312530625}
  - {fileID: 4712292338687573921}
  - {fileID: 7700676329067016366}
  m_BlendShapeWeights: []
  m_RootBone: {fileID: 4303522748295484652}
  m_AABB:
    m_Center: {x: -0.0010192692, y: 0.46571648, z: -0.12249104}
    m_Extent: {x: 0.091920495, y: 0.17998058, z: 0.1596455}
  m_DirtyAABB: 0
--- !u!1 &6517966638275177130
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5483940262883225768}
  m_Layer: 0
  m_Name: middle_03_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5483940262883225768
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6517966638275177130}
  serializedVersion: 2
  m_LocalRotation: {x: -0.007730959, y: 0.00016430892, z: 0.0217835, w: 0.99973285}
  m_LocalPosition: {x: 0.000037231446, y: 0.02905731, z: 0.000018892288}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5113790155137111776}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6572088561083704202
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5945311810865105750}
  m_Layer: 8
  m_Name: body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5945311810865105750
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6572088561083704202}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6609913341895952615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4495432798587655918}
  m_Layer: 8
  m_Name: hammer
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4495432798587655918
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6609913341895952615}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.035133835, z: -0.07476188}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6696311079637022582
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5647550049040330743}
  - component: {fileID: 5769118334450509500}
  - component: {fileID: 8049098597803841745}
  - component: {fileID: 884123513665906388}
  m_Layer: 9
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5647550049040330743
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6696311079637022582}
  serializedVersion: 2
  m_LocalRotation: {x: -0.000269411, y: 0.00016035743, z: 0.9997506, w: 0.02232957}
  m_LocalPosition: {x: 0.0974114, y: -0.021429218, z: -0.0022248626}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9039848297957138845}
  m_Father: {fileID: 1575208649713631053}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5769118334450509500
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6696311079637022582}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &8049098597803841745
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6696311079637022582}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.1078396
  m_Height: 0.5272158
  m_Direction: 1
  m_Center: {x: 0.00000011920929, y: 0.23964323, z: -0.000000059306323}
--- !u!153 &884123513665906388
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6696311079637022582}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6615271875373956784}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &6928186511083805196
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4712292338687573921}
  m_Layer: 0
  m_Name: CC_Base_R_RibsTwist
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4712292338687573921
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6928186511083805196}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0010908963, y: 0.6153887, z: 0.78822196, w: -0.0013186043}
  m_LocalPosition: {x: 0.09821229, y: 0.047898404, z: 0.14247963}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2941098901988364380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6950307348670214137
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 547697443837435824}
  m_Layer: 8
  m_Name: CompassArrow
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &547697443837435824
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6950307348670214137}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 1.707269}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5497525267918870652}
  - {fileID: 4066937904800744613}
  m_Father: {fileID: 8083330852181475221}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &6960240698100956915
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 306422539164200583}
  m_Layer: 8
  m_Name: Pistol_00
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &306422539164200583
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 6960240698100956915}
  serializedVersion: 2
  m_LocalRotation: {x: 0.11011008, y: -0.19348438, z: -0.7710402, w: 0.59660435}
  m_LocalPosition: {x: -0.003746055, y: 0.07840997, z: 0.10322397}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5945311810865105750}
  - {fileID: 4495432798587655918}
  - {fileID: 2381486250098498953}
  - {fileID: 4970681250608070657}
  - {fileID: 2061383217299137637}
  - {fileID: 2423114747879000155}
  - {fileID: 7904709490601092112}
  - {fileID: 9200543034088440229}
  - {fileID: 3645407059040468473}
  - {fileID: 1307319351738626410}
  - {fileID: 8475396748067272981}
  m_Father: {fileID: 3183515333007324220}
  m_LocalEulerAnglesHint: {x: -9.613, y: -23.977, z: -102.491}
--- !u!1 &7010696862868051775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9200543034088440229}
  m_Layer: 8
  m_Name: Left Hand Target
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9200543034088440229
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7010696862868051775}
  serializedVersion: 2
  m_LocalRotation: {x: 0.79035556, y: -0.33337912, z: 0.37890536, w: -0.34731433}
  m_LocalPosition: {x: 0.005, y: -0.053, z: -0.039}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 55.4101, y: -238.4923, z: -34.3354}
--- !u!1 &7163592225318479535
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7700676329067016366}
  m_Layer: 0
  m_Name: clavicle_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7700676329067016366
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7163592225318479535}
  serializedVersion: 2
  m_LocalRotation: {x: 0.009416457, y: 0.16666275, z: -0.6722112, w: 0.7212953}
  m_LocalPosition: {x: 0.047479425, y: 0.21393646, z: 0.0025744247}
  m_LocalScale: {x: 0.99999994, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 196917263236161228}
  m_Father: {fileID: 2941098901988364380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7200678715925478581
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7678416043862309964}
  m_Layer: 8
  m_Name: RaycasterFists
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7678416043862309964
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7200678715925478581}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.2, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7202043787398676212
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3624260035379648237}
  - component: {fileID: 7209225626478333758}
  m_Layer: 0
  m_Name: neck_01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3624260035379648237
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7202043787398676212}
  serializedVersion: 2
  m_LocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
  m_LocalPosition: {x: 0.0000000077486035, y: 0.26940826, z: -0.0000020599364}
  m_LocalScale: {x: 0.9999999, y: 1.0000001, z: 1.0000002}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6187010263963786470}
  - {fileID: 2813250818033119342}
  m_Father: {fileID: 2941098901988364380}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &7209225626478333758
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7202043787398676212}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 45281828b4c9247558c7c695124d6877, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  axis: {x: -1, y: 0, z: 0}
  defaultLocalRotation: {x: 0.26952836, y: -0.0010340491, z: 0.0076741115, w: 0.9629613}
  limit: 5
  twistLimit: 5
--- !u!1 &7327014100500261307
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7237376372815333097}
  m_Layer: 8
  m_Name: VaultCharRot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7237376372815333097
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7327014100500261307}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 1, z: 0, w: -0.00000004371139}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1983295984226834791}
  m_Father: {fileID: 1791615436228211716}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7362992543243148219
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9217853567996125564}
  - component: {fileID: 2320732454331389162}
  - component: {fileID: 7795496543920018573}
  - component: {fileID: 7727700648032867458}
  m_Layer: 0
  m_Name: calf_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9217853567996125564
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7362992543243148219}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005481533, y: 0.0001228251, z: -0.022256006, w: 0.99973726}
  m_LocalPosition: {x: -0.000000538826, y: 0.47930038, z: -0.000000629425}
  m_LocalScale: {x: 1.0000002, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 761683113489791214}
  - {fileID: 8169557302639763551}
  m_Father: {fileID: 2993532018169991005}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &2320732454331389162
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7362992543243148219}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &7795496543920018573
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7362992543243148219}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705828
  m_Height: 0.54307145
  m_Direction: 1
  m_Center: {x: 0.00000021979207, y: 0.24685049, z: -0.0000003026798}
--- !u!153 &7727700648032867458
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7362992543243148219}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 7966915517607231001}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 0, y: 0, z: 1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: -1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.62689
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.37311
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7375942487646971822
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2009526134938540747}
  - component: {fileID: 382044195611034870}
  - component: {fileID: 7048467088361206421}
  - component: {fileID: 1960802193429306960}
  m_Layer: 0
  m_Name: thigh_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2009526134938540747
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7375942487646971822}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00026941087, y: 0.00016035211, z: 0.9997506, w: 0.02232957}
  m_LocalPosition: {x: 0.0974114, y: -0.021429215, z: -0.0022249054}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 6553216298185710679}
  - {fileID: 1871936065825758296}
  m_Father: {fileID: 5928552661432004395}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &382044195611034870
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7375942487646971822}
  serializedVersion: 5
  m_Mass: 7.4100003
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &7048467088361206421
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7375942487646971822}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.1078396
  m_Height: 0.5272158
  m_Direction: 1
  m_Center: {x: 0.00000011920929, y: 0.23964323, z: -0.000000059306323}
--- !u!153 &1960802193429306960
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7375942487646971822}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 63165348324869592}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -120
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 35
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7404040268484633181
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6427481077866857254}
  - component: {fileID: 797826381359144424}
  - component: {fileID: 3124112344989593694}
  - component: {fileID: 4150183783532615535}
  m_Layer: 9
  m_Name: upperarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6427481077866857254
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7404040268484633181}
  serializedVersion: 2
  m_LocalRotation: {x: -0.026243053, y: 0.026773466, z: 0.71181405, w: 0.7013668}
  m_LocalPosition: {x: -0.14536102, y: 0.33108318, z: -0.08794619}
  m_LocalScale: {x: 0.99999976, y: 0.9999999, z: 0.99999976}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1265225660549391993}
  m_Father: {fileID: 6475188008645147268}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &797826381359144424
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7404040268484633181}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &3124112344989593694
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7404040268484633181}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08439919
  m_Height: 0.30946368
  m_Direction: 1
  m_Center: {x: 0.0000002495945, y: 0.14066541, z: -0.0000000111758744}
--- !u!153 &4150183783532615535
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7404040268484633181}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 4016410368822921293}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7452853504100433673
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4145270954867361742}
  m_Layer: 8
  m_Name: GroundRaycaster2
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4145270954867361742
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7452853504100433673}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.00000018618678, z: 0, w: 1}
  m_LocalPosition: {x: 0.000000018157792, y: 0, z: 0.19933805}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3341596775319770571}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7531102765966216775
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7904709490601092112}
  m_Layer: 8
  m_Name: trigger
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7904709490601092112
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7531102765966216775}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.015382193, z: -0.0020682926}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7563218313809299764
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5089675981995570609}
  m_Layer: 8
  m_Name: SubModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5089675981995570609
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7563218313809299764}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4690863144980786626}
  - {fileID: 3044181435835765161}
  m_Father: {fileID: 5344600829119329324}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7582251552950000228
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1265225660549391993}
  - component: {fileID: 5535407339797768801}
  - component: {fileID: 3005204020033022147}
  - component: {fileID: 723129614767656655}
  m_Layer: 9
  m_Name: lowerarm_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1265225660549391993
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7582251552950000228}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00000048987545, y: -0.000012127677, z: -0.004907473, w: 0.99998796}
  m_LocalPosition: {x: -0.0000004894101, y: 0.28133073, z: -0.00000010879015}
  m_LocalScale: {x: 1.0000001, y: 1.0000002, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 336202965966922876}
  m_Father: {fileID: 6427481077866857254}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5535407339797768801
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7582251552950000228}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &3005204020033022147
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7582251552950000228}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075959265
  m_Height: 0.2736391
  m_Direction: 1
  m_Center: {x: 0.0000005689216, y: 0.12438155, z: -0.00000009420073}
--- !u!153 &723129614767656655
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7582251552950000228}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 797826381359144424}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.5623221
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.43768
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7598083152590214437
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7360410177784678560}
  m_Layer: 0
  m_Name: thumb_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7360410177784678560
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7598083152590214437}
  serializedVersion: 2
  m_LocalRotation: {x: -0.1623765, y: 0.004189572, z: -0.09236491, w: 0.9823874}
  m_LocalPosition: {x: 0.0000007629394, y: 0.054367825, z: -0.0000007724762}
  m_LocalScale: {x: 1.0000001, y: 1, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 3925619699002202038}
  m_Father: {fileID: 9209227907606372116}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7642154614732580612
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5928552661432004395}
  - component: {fileID: 63165348324869592}
  - component: {fileID: 5565864110821386065}
  - component: {fileID: 7691311325594940460}
  m_Layer: 0
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5928552661432004395
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7642154614732580612}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7071063, y: 0.000010983767, z: -0.000016404654, w: 0.70710725}
  m_LocalPosition: {x: -0, y: 0, z: 1.1449527}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4777220526592169751}
  - {fileID: 2993532018169991005}
  - {fileID: 2009526134938540747}
  m_Father: {fileID: 6961841487412310924}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &63165348324869592
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7642154614732580612}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &5565864110821386065
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7642154614732580612}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23321414, y: 0.17533009, z: 0.13992849}
  m_Center: {x: 0.0000044474327, y: 0.028164964, z: 0.016374627}
--- !u!153 &7691311325594940460
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7642154614732580612}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7673882911262201615
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8025193364246868120}
  m_Layer: 0
  m_Name: upperarm_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8025193364246868120
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7673882911262201615}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000019607223, y: -0.00000022029232, z: 0.0000012521631,
    w: 1}
  m_LocalPosition: {x: 0.00000045776366, y: 0.14070854, z: 0.0000006866455}
  m_LocalScale: {x: 1.0000001, y: 0.99999994, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 196917263236161228}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7681190465600941532
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1926055358864674220}
  m_Layer: 0
  m_Name: middle_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1926055358864674220
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7681190465600941532}
  serializedVersion: 2
  m_LocalRotation: {x: -0.00771825, y: -0.00016319613, z: -0.021914285, w: 0.9997301}
  m_LocalPosition: {x: -0.000027160644, y: 0.028993454, z: 0.0000051403044}
  m_LocalScale: {x: 1.0000001, y: 1.0000001, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 3765449231651182655}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7723577181372818986
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9136109770109313250}
  - component: {fileID: 5920715593122831471}
  - component: {fileID: 4688330279960404796}
  - component: {fileID: 4055683562605038964}
  m_Layer: 9
  m_Name: hand_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9136109770109313250
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7723577181372818986}
  serializedVersion: 2
  m_LocalRotation: {x: 0.102513105, y: -0.0019429321, z: 0.026160488, w: 0.9943857}
  m_LocalPosition: {x: 0.00000017648561, y: 0.24901599, z: 0.00000016253436}
  m_LocalScale: {x: 0.99999994, y: 0.9999999, z: 0.9999999}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 700223709043250698}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5920715593122831471
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7723577181372818986}
  serializedVersion: 5
  m_Mass: 0.495
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &4688330279960404796
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7723577181372818986}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.07003571
  m_Height: 0.20543809
  m_Direction: 1
  m_Center: {x: -0.0000005289913, y: 0.09338101, z: -0.0000000018626454}
--- !u!153 &4055683562605038964
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7723577181372818986}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 8418904364804336267}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -50
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 50
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 25
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7858538707774411489
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 700223709043250698}
  - component: {fileID: 8418904364804336267}
  - component: {fileID: 3037784869323015799}
  - component: {fileID: 3686158052654258277}
  m_Layer: 9
  m_Name: lowerarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &700223709043250698
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7858538707774411489}
  serializedVersion: 2
  m_LocalRotation: {x: 0.0000016186383, y: 0.000018548217, z: 0.007330766, w: 0.9999731}
  m_LocalPosition: {x: 0.0000007487835, y: 0.28141713, z: 0.0000009151411}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 9136109770109313250}
  m_Father: {fileID: 4099452736357410725}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &8418904364804336267
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7858538707774411489}
  serializedVersion: 5
  m_Mass: 1.2
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &3037784869323015799
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7858538707774411489}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.075982586
  m_Height: 0.27391738
  m_Direction: 1
  m_Center: {x: -0.00000032421664, y: 0.12450803, z: 0.0000000035406056}
--- !u!153 &3686158052654258277
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7858538707774411489}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 3393522759107121190}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -0.8402332
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 139.15976
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7881397944302175594
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 9130952369729836117}
  m_Layer: 0
  m_Name: CC_Base_JawRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &9130952369729836117
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7881397944302175594}
  serializedVersion: 2
  m_LocalRotation: {x: 0.000003424143, y: -0.00006812931, z: 1, w: -0.000017310314}
  m_LocalPosition: {x: 0.026865939, y: 0.011197052, z: -0.00015041605}
  m_LocalScale: {x: 1, y: 1.0000001, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 394745373956028053}
  - {fileID: 3180524282443571956}
  m_Father: {fileID: 4490211272603021980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &7915872216967400469
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6553216298185710679}
  - component: {fileID: 3344840827136345536}
  - component: {fileID: 1831407085850407401}
  - component: {fileID: 7149468115753376637}
  m_Layer: 0
  m_Name: calf_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6553216298185710679
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7915872216967400469}
  serializedVersion: 2
  m_LocalRotation: {x: -0.005145941, y: -0.0001241103, z: 0.024217186, w: 0.99969345}
  m_LocalPosition: {x: 0.00000020980835, y: 0.4792871, z: -0.00000022649765}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5521858126448997138}
  - {fileID: 1118397962452466628}
  m_Father: {fileID: 2009526134938540747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &3344840827136345536
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7915872216967400469}
  serializedVersion: 5
  m_Mass: 3.4875002
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &1831407085850407401
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7915872216967400469}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.09705563
  m_Height: 0.5431391
  m_Direction: 1
  m_Center: {x: -0.00000021420419, y: 0.24688114, z: -0.00000020395967}
--- !u!153 &7149468115753376637
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7915872216967400469}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 382044195611034870}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: -0, y: -0, z: -1}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 1, y: 0, z: 0}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -2.837384
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 137.16261
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 10
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &7980709107492002770
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5228426364522323745}
  - component: {fileID: 6368213498894284602}
  m_Layer: 9
  m_Name: SphereDetectSensor
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5228426364522323745
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7980709107492002770}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8327770814347762760}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!135 &6368213498894284602
SphereCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 7980709107492002770}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 1048576
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 1048575
  m_LayerOverridePriority: 0
  m_IsTrigger: 1
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Radius: 7
  m_Center: {x: 0, y: 0, z: 0}
--- !u!1 &8007172836234142032
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1575208649713631053}
  - component: {fileID: 6615271875373956784}
  - component: {fileID: 5187918387588122668}
  - component: {fileID: 5609573648203050230}
  m_Layer: 9
  m_Name: pelvis
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1575208649713631053
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8007172836234142032}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000006556511, y: -0.0000038331455, z: -0.00001936654, w: 1}
  m_LocalPosition: {x: -0.0000029843027, y: 1.1449528, z: -0.03379741}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4410766268693251201}
  - {fileID: 5647550049040330743}
  - {fileID: 6475188008645147268}
  m_Father: {fileID: 8327770814347762760}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &6615271875373956784
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8007172836234142032}
  serializedVersion: 5
  m_Mass: 12.700001
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!65 &5187918387588122668
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8007172836234142032}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 0.23321414, y: 0.17533009, z: 0.13992849}
  m_Center: {x: 0.0000044474327, y: 0.028164964, z: 0.016374627}
--- !u!153 &5609573648203050230
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8007172836234142032}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 0}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 1, z: 0}
  m_XMotion: 2
  m_YMotion: 2
  m_ZMotion: 2
  m_AngularXMotion: 2
  m_AngularYMotion: 2
  m_AngularZMotion: 2
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8143742805387103593
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 47519501808106740}
  m_Layer: 8
  m_Name: CameraPosition
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &47519501808106740
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8143742805387103593}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.45, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8312298949538763238
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3765449231651182655}
  m_Layer: 0
  m_Name: middle_02_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3765449231651182655
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8312298949538763238}
  serializedVersion: 2
  m_LocalRotation: {x: 0.01329394, y: 0.0036442443, z: -0.006823158, w: 0.99988174}
  m_LocalPosition: {x: -0.00000061035155, y: 0.044478226, z: -0.0000003147125}
  m_LocalScale: {x: 0.9999999, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1926055358864674220}
  m_Father: {fileID: 7133569396811251066}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8324685010430835945
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 4247056872505880032}
  - component: {fileID: 581550573166700597}
  m_Layer: 0
  m_Name: CC_Base_Body
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &4247056872505880032
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8324685010430835945}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &581550573166700597
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8324685010430835945}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 200bbf6c0111b754da4a1475237e4df7, type: 2}
  - {fileID: 2100000, guid: d5304ebfac9fe2c4da18d9d33afa376e, type: 2}
  - {fileID: 2100000, guid: 628c86a6f9625f5469de5a363e069635, type: 2}
  - {fileID: 2100000, guid: be5d7ee1b1c38c24494e713614d5d56c, type: 2}
  - {fileID: 2100000, guid: 1f841e0bd7216ea41ac5c812b7117b12, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 2988033852023935963, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 7669328701674395276}
  - {fileID: 8683188798031981173}
  - {fileID: 5928552661432004395}
  - {fileID: 4303522748295484652}
  - {fileID: 4777220526592169751}
  - {fileID: 2941098901988364380}
  - {fileID: 6187010263963786470}
  - {fileID: 9130952369729836117}
  - {fileID: 3624260035379648237}
  - {fileID: 2993532018169991005}
  - {fileID: 2009526134938540747}
  - {fileID: 3984984611843329466}
  - {fileID: 4149227583924101474}
  - {fileID: 9217853567996125564}
  - {fileID: 761683113489791214}
  - {fileID: 8169557302639763551}
  - {fileID: 3484386517536547667}
  - {fileID: 2753697593838296547}
  - {fileID: 8172053399810076699}
  - {fileID: 5113790155137111776}
  - {fileID: 6251702348690273879}
  - {fileID: 5483940262883225768}
  - {fileID: 5572791610057042807}
  - {fileID: 1887670676407111013}
  - {fileID: 2061066770635408989}
  - {fileID: 3366948161025063600}
  - {fileID: 2310010653122767096}
  - {fileID: 1481966550462016051}
  - {fileID: 4053749872605063169}
  - {fileID: 5078890076475787187}
  - {fileID: 9209227907606372116}
  - {fileID: 7360410177784678560}
  - {fileID: 3925619699002202038}
  - {fileID: 8731906559322667490}
  - {fileID: 4605998933334557323}
  - {fileID: 4685813500312530625}
  - {fileID: 4712292338687573921}
  - {fileID: 7700676329067016366}
  - {fileID: 469256491857063785}
  - {fileID: 6926629222984784688}
  - {fileID: 8025193364246868120}
  - {fileID: 196917263236161228}
  - {fileID: 6553216298185710679}
  - {fileID: 5521858126448997138}
  - {fileID: 1118397962452466628}
  - {fileID: 1871936065825758296}
  - {fileID: 7133569396811251066}
  - {fileID: 3765449231651182655}
  - {fileID: 2246782681655151770}
  - {fileID: 5211065926650188693}
  - {fileID: 1926055358864674220}
  - {fileID: 4797740664379296513}
  - {fileID: 5865011514679355812}
  - {fileID: 1502515452299025953}
  - {fileID: 3717996316510139074}
  - {fileID: 3790389470756885847}
  - {fileID: 3662348311763307833}
  - {fileID: 7065130496213050685}
  - {fileID: 5562929773770824881}
  - {fileID: 7556803309533903411}
  - {fileID: 9220517768640357345}
  - {fileID: 7781808719370263609}
  - {fileID: 4056800010831387403}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 5928552661432004395}
  m_AABB:
    m_Center: {x: 0.00039353967, y: 0.5694388, z: 0.004483696}
    m_Extent: {x: 0.8668691, y: 0.162474, z: 0.102908105}
  m_DirtyAABB: 0
--- !u!1 &8340815443802278568
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1502515452299025953}
  m_Layer: 0
  m_Name: index_03_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1502515452299025953
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8340815443802278568}
  serializedVersion: 2
  m_LocalRotation: {x: -0.015386808, y: -0.0002748661, z: -0.0053034103, w: 0.99986756}
  m_LocalPosition: {x: 0.00045135498, y: 0.028107528, z: -0.0007394695}
  m_LocalScale: {x: 1.0000001, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5865011514679355812}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8347644058308767983
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2331077866999755286}
  m_Layer: 8
  m_Name: AimHelper
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2331077866999755286
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8347644058308767983}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5127146508601231717}
  - {fileID: 6667973866451987449}
  - {fileID: 188281107476723005}
  - {fileID: 3936308218292781600}
  - {fileID: 6691559850851508290}
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8351542347483321115
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5344600829119329324}
  - component: {fileID: 6617492742549939772}
  - component: {fileID: 856916742753038200}
  m_Layer: 8
  m_Name: AimingModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5344600829119329324
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8351542347483321115}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5258167489492070385}
  - {fileID: 5089675981995570609}
  m_Father: {fileID: 2195793653869371303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &6617492742549939772
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8351542347483321115}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0dcbffa32d899b4cac41b83a61cef7d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 3258125153270715800}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <SubModules>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[SubModule.Aiming.AimingSubModule,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <CanUpdate>k__BackingField: 0
  <ControllerIndex>k__BackingField: 0
  mainState: 000000000100000002000000
  subState: 0
  <SubModuleState>k__BackingField: 0
  <InputName>k__BackingField: 0300000004000000
  hasRefreshRate: 0
  refreshRate: 0
  executionPriority: 0
  m_lookAtIK: {fileID: 368280542376683019}
  ragdollAiming: {fileID: 856916742753038200}
  autoAiming: 0
  aimingConfig: {fileID: 11400000, guid: f04069f0cddf67241b1ab5b71edc6b32, type: 2}
  currentConeAngle: 0
  coneRange: 6
  playerTransform: {fileID: 8775929964290693195}
  aimHelperTransform: {fileID: 2813250818033119342}
  m_isForwardAiming: 0
  m_aimTarget: {fileID: 6691559850851508290}
  m_forwardAimTarget: {fileID: 3936308218292781600}
  m_targetDetected: 0
  m_targetName: 
  m_lastTarget:
    x: 0
    y: 0
    z: 0
  m_isInFOV: 0
  m_currentScore: 0
  m_detectSensorBase: {fileID: 7725524678241424973}
  m_canSwitchTarget: 1
  m_characterParameters: {fileID: 4766468187706380211}
  m_closestTargetDistanceThreshold: 0.01
  m_isAnyTargetBehind: 0
  _cooldownEndTime: -1
  m_cachedDetectedEnemies: []
  m_cachedCurrentTargetPosition:
    x: 0
    y: 0
    z: 0
  m_hasCachedTarget: 0
--- !u!114 &856916742753038200
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8351542347483321115}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0a9db18cd58f72348905fc8ae0a482d3, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  puppetMaster: {fileID: 7905871042871541643}
  aimIKBeforePhysics: {fileID: 1926729403111026202}
  target: {fileID: 6691559850851508290}
  fixAiming: 1
  fixLeftHand: 1
  aimIKAfterPhysics: {fileID: 6863889055913149302}
  hasLeftHandIK: 0
  leftHandIK: {fileID: 6535798778689592834}
  leftHandTarget: {fileID: 0}
  weight: 0
  targetSwitchSmoothTime: 0.35
  weightSmoothTime: 0.35
  smoothTurnTowardsTarget: 1
  maxRadiansDelta: 3
  maxMagnitudeDelta: 3
  slerpSpeed: 3
  smoothDampTime: 0
  pivotOffsetFromRoot: {x: 0, y: 1, z: 0}
  minDistance: 1
  offset: {x: 0, y: 0, z: 0}
  maxRootAngle: 0
  turnToTarget: 0
  turnToTargetTime: 0.5
  useAnimatedAimDirection: 0
  animatedAimDirection: {x: 0, y: 0, z: 1}
  m_currentAimingData:
    HasLeftHandIK: 0
    LeftHandFixTransform: {fileID: 0}
    AimTarget: {fileID: 0}
    Pointer: {fileID: 0}
  animator: {fileID: 2713062770034213158}
--- !u!1 &8393138137184261682
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8083330852181475221}
  - component: {fileID: 699025248306278272}
  m_Layer: 8
  m_Name: InputDirectionCompass
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8083330852181475221
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393138137184261682}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.4979999, y: 0, z: 0.01}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 547697443837435824}
  m_Father: {fileID: 2693233403027579995}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &699025248306278272
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8393138137184261682}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 1db305b54b5c3934dbcbddbda5d14f5d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes: []
  <ControllerIndex>k__BackingField: 0
  characterParameter: {fileID: 4766468187706380211}
  reachedTarget: 0
  CharacterPositionCompass: {fileID: 197955182703635422}
  minMagnitude: 0.01
  lastRotation: {x: 0, y: 0, z: 0, w: 0}
  desiredRotation: {x: 0, y: 0, z: 0, w: 0}
  speed: 25
  <MainState>k__BackingField: 000000000100000002000000
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  <SubState>k__BackingField: 0
  <InputName>k__BackingField: 02000000
  m_inputVector: {x: 0, y: 0, z: 0}
--- !u!1 &8438348666980782325
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7247426993471718996}
  - component: {fileID: 1443667753020185120}
  - component: {fileID: 7811239351625802803}
  m_Layer: 8
  m_Name: WeaponModule
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &7247426993471718996
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8438348666980782325}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 8373701522075401058}
  - {fileID: 502270518393233424}
  m_Father: {fileID: 2195793653869371303}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &1443667753020185120
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8438348666980782325}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 88226de0bc8347118b6651808977535d, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 407324655579810657}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <SubModules>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[WeaponSubModuleState, General],[SubModule.Weapon.WeaponSubModule,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.EnumEqualityComparer`1[[WeaponSubModuleState,
        General]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 3
      Data: 0
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <ControllerIndex>k__BackingField: 0
  m_weaponIndex: 0
  <MainState>k__BackingField: 000000000100000002000000
  _subModuleState: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  animationModule: {fileID: 7811239351625802803}
  m_CurrentSubModuleState: {fileID: 0}
  <InputName>k__BackingField: 1200000013000000030000001600000017000000
  _weaponSubState: 0
  m_isShooting: 0
  m_isAiming: 0
  m_autoShooting: 0
  m_lastShootingState: 0
  m_detectedTarget: 0
  <CurrentBulletType>k__BackingField: 0
  BulletPrefab: {fileID: 1218724716591446, guid: 0e2eba3d3c3303446ab3f67d8a25d6b3,
    type: 3}
  BulletPrefabs: []
  bulletTypeData: {fileID: 11400000, guid: 87db5caf701a66740a65c07309226b52, type: 2}
  bulletSpeed: 0
  spreadMultiplier: 1
--- !u!114 &7811239351625802803
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8438348666980782325}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8691dd0ae36145d88c17f855c2eb91ba, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 5631959735350184254}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <States>k__BackingField
      Entry: 7
      Data: 0|System.Collections.Generic.Dictionary`2[[System.Enum, mscorlib],[Module.Mono.Animancer.RealsticFemale.WeaponState,
        Assembly-CSharp]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 1|System.Collections.Generic.ObjectEqualityComparer`1[[System.Enum, mscorlib]],
        mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 1
    - Name: 
      Entry: 7
      Data: 
    - Name: $k
      Entry: 7
      Data: 2|WeaponSubModuleState, General
    - Name: 
      Entry: 3
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: $v
      Entry: 10
      Data: 0
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  m_useAnimancer: 0
  <ControllerIndex>k__BackingField: 0
  <MainState>k__BackingField: 000000000100000002000000
  <SubState>k__BackingField: 0
  <HasRefreshRate>k__BackingField: 0
  <RefreshRate>k__BackingField: 0
  _Animancer: {fileID: 9096211446228400863}
  _Parameters: {fileID: 4766468187706380211}
  DynamicLayeredCharacterAnimations: {fileID: 746262152108682085}
  _subModuleState: 0
  m_autoAiming: 0
  m_detectTarget: 0
  enableEventDrivenAnimation: 1
  enableUpperBodyAnimations: 1
  enableWeaponObjectAnimations: 1
  enableDebugLogging: 0
  animationLODDistance: 50
  maxConcurrentAnimations: 5
  enableAnimationPooling: 1
  <InputName>k__BackingField: 040000000300000013000000
  <State>k__BackingField: 0
--- !u!1 &8444073851638498965
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 5113790155137111776}
  m_Layer: 0
  m_Name: middle_02_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &5113790155137111776
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8444073851638498965}
  serializedVersion: 2
  m_LocalRotation: {x: 0.013295288, y: -0.003645902, z: 0.0070136134, w: 0.9998804}
  m_LocalPosition: {x: 0.00000061035155, y: 0.044478912, z: -0.00000022888183}
  m_LocalScale: {x: 1, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5483940262883225768}
  m_Father: {fileID: 8172053399810076699}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8547210156153238776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 196917263236161228}
  - component: {fileID: 5562249760951509835}
  - component: {fileID: 502339555902481935}
  - component: {fileID: 7449202744803335089}
  m_Layer: 0
  m_Name: upperarm_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &196917263236161228
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8547210156153238776}
  serializedVersion: 2
  m_LocalRotation: {x: 0.117331214, y: -0.0024746358, z: -0.027261361, w: 0.99271554}
  m_LocalPosition: {x: -0.00000015258789, y: 0.100599535, z: -0.0000004816055}
  m_LocalScale: {x: 0.99999976, y: 0.99999964, z: 0.99999964}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 469256491857063785}
  - {fileID: 8025193364246868120}
  m_Father: {fileID: 7700676329067016366}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!54 &5562249760951509835
Rigidbody:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8547210156153238776}
  serializedVersion: 5
  m_Mass: 2.025
  m_LinearDamping: 0
  m_AngularDamping: 0.05
  m_CenterOfMass: {x: 0, y: 0, z: 0}
  m_InertiaTensor: {x: 1, y: 1, z: 1}
  m_InertiaRotation: {x: 0, y: 0, z: 0, w: 1}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ImplicitCom: 1
  m_ImplicitTensor: 1
  m_UseGravity: 1
  m_IsKinematic: 0
  m_Interpolate: 0
  m_Constraints: 0
  m_CollisionDetection: 0
--- !u!136 &502339555902481935
CapsuleCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8547210156153238776}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 2
  m_Radius: 0.08442511
  m_Height: 0.30955872
  m_Direction: 1
  m_Center: {x: -0.00000006286429, y: 0.14070858, z: 0.00000039115562}
--- !u!153 &7449202744803335089
ConfigurableJoint:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8547210156153238776}
  serializedVersion: 4
  m_ConnectedBody: {fileID: 6469044919349165650}
  m_ConnectedArticulationBody: {fileID: 0}
  m_Anchor: {x: 0, y: 0, z: 0}
  m_Axis: {x: 1, y: 0, z: 0}
  m_AutoConfigureConnectedAnchor: 1
  m_ConnectedAnchor: {x: 0, y: 0, z: 0}
  m_SecondaryAxis: {x: 0, y: 0, z: 1}
  m_XMotion: 0
  m_YMotion: 0
  m_ZMotion: 0
  m_AngularXMotion: 1
  m_AngularYMotion: 1
  m_AngularZMotion: 1
  m_LinearLimitSpring:
    spring: 0
    damper: 0
  m_LinearLimit:
    limit: 0
    bounciness: 0
    contactDistance: 0
  m_AngularXLimitSpring:
    spring: 0
    damper: 0
  m_LowAngularXLimit:
    limit: -35
    bounciness: 0
    contactDistance: 0
  m_HighAngularXLimit:
    limit: 120
    bounciness: 0
    contactDistance: 0
  m_AngularYZLimitSpring:
    spring: 0
    damper: 0
  m_AngularYLimit:
    limit: 85
    bounciness: 0
    contactDistance: 0
  m_AngularZLimit:
    limit: 45
    bounciness: 0
    contactDistance: 0
  m_TargetPosition: {x: 0, y: 0, z: 0}
  m_TargetVelocity: {x: 0, y: 0, z: 0}
  m_XDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_YDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_TargetRotation: {x: 0, y: 0, z: 0, w: 1}
  m_TargetAngularVelocity: {x: 0, y: 0, z: 0}
  m_RotationDriveMode: 0
  m_AngularXDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_AngularYZDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_SlerpDrive:
    serializedVersion: 4
    positionSpring: 0
    positionDamper: 0
    maximumForce: 3.4028233e+38
    useAcceleration: 0
  m_ProjectionMode: 0
  m_ProjectionDistance: 0.1
  m_ProjectionAngle: 180
  m_ConfiguredInWorldSpace: 0
  m_SwapBodies: 0
  m_BreakForce: Infinity
  m_BreakTorque: Infinity
  m_EnableCollision: 0
  m_EnablePreprocessing: 1
  m_MassScale: 1
  m_ConnectedMassScale: 1
--- !u!1 &8631535747628063895
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3686526413400921503}
  m_Layer: 0
  m_Name: CC_Base_L_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3686526413400921503
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8631535747628063895}
  serializedVersion: 2
  m_LocalRotation: {x: -0.49999708, y: 0.5000029, z: 0.49999708, w: 0.5000029}
  m_LocalPosition: {x: 0.06261502, y: 0.05849472, z: 0.03165966}
  m_LocalScale: {x: 1, y: 1, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 4490211272603021980}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8643798026416708398
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 8683188798031981173}
  m_Layer: 0
  m_Name: lowerarm_twist_01_l
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &8683188798031981173
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8643798026416708398}
  serializedVersion: 2
  m_LocalRotation: {x: -0.0000020261016, y: -0.0000023856562, z: -0.0000005890616,
    w: 1}
  m_LocalPosition: {x: 0.0000016784668, y: 0.12341133, z: -0.0000041913986}
  m_LocalScale: {x: 1, y: 0.99999994, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 7669328701674395276}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &8786989549537887712
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2693233403027579995}
  - component: {fileID: 5218099212934094078}
  - component: {fileID: 4766468187706380211}
  - component: {fileID: 202015275302094361}
  - component: {fileID: 9111336882492878132}
  - component: {fileID: 746262152108682085}
  - component: {fileID: 4937370481206954850}
  - component: {fileID: 5212865479793037668}
  m_Layer: 8
  m_Name: Female1Character
  m_TagString: Player
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2693233403027579995
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 5377096867207176154}
  - {fileID: 1067468565911564907}
  - {fileID: 8083330852181475221}
  - {fileID: 2837586148865164468}
  - {fileID: 8581761479138929668}
  - {fileID: 8327770814347762760}
  - {fileID: 3304145376778902292}
  - {fileID: 8775929964290693195}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &5218099212934094078
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a14bfc2338bf4611b10d398151e4dca8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects:
    - {fileID: 2638255759238724534}
    - {fileID: 699025248306278272}
    - {fileID: 1443667753020185120}
    - {fileID: 7811239351625802803}
    - {fileID: 8684649439442927484}
    - {fileID: 3243832241670686071}
    - {fileID: 6617492742549939772}
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: _modules
      Entry: 7
      Data: 0|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 7
    - Name: 
      Entry: 10
      Data: 0
    - Name: 
      Entry: 10
      Data: 1
    - Name: 
      Entry: 10
      Data: 2
    - Name: 
      Entry: 10
      Data: 3
    - Name: 
      Entry: 10
      Data: 4
    - Name: 
      Entry: 10
      Data: 5
    - Name: 
      Entry: 10
      Data: 6
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: nextUpdateTimes
      Entry: 7
      Data: 1|System.Collections.Generic.Dictionary`2[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp],[System.Single, mscorlib]], mscorlib
    - Name: comparer
      Entry: 7
      Data: 2|System.Collections.Generic.ObjectEqualityComparer`1[[DefaultNamespace.Mono.Interface.IModule,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 8
      Data: 
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
    - Name: _needComponents
      Entry: 7
      Data: 3|System.Collections.Generic.List`1[[DefaultNamespace.Mono.Interface.INeedComponent,
        Assembly-CSharp]], mscorlib
    - Name: 
      Entry: 12
      Data: 0
    - Name: 
      Entry: 13
      Data: 
    - Name: 
      Entry: 8
      Data: 
  <ControllerIndex>k__BackingField: 0
  IsInitialized: 0
  StateManager: {fileID: 202015275302094361}
  RagdollParent: {fileID: 8327770814347762760}
  CharacterParent: {fileID: 8775929964290693195}
  Colliders:
  - {fileID: 5228426364522323745}
  - {fileID: 1575208649713631053}
  - {fileID: 4410766268693251201}
  - {fileID: 119864837713895696}
  - {fileID: 4612088608534634171}
  - {fileID: 5647550049040330743}
  - {fileID: 9039848297957138845}
  - {fileID: 1845394404451702405}
  - {fileID: 6475188008645147268}
  - {fileID: 6427481077866857254}
  - {fileID: 1265225660549391993}
  - {fileID: 4099452736357410725}
  - {fileID: 700223709043250698}
--- !u!114 &4766468187706380211
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: a1895a8d88d74273ba2e4fb0324e66e0, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: <CurrentState>k__BackingField
      Entry: 6
      Data: 
  m_animator: {fileID: 2713062770034213158}
  Horizontal:
    EnableLearping: 0
    Timer: 0.05
    Value: 0
  Vertical:
    EnableLearping: 0
    Timer: 0.05
    Value: 0
  IsAiming: 0
  LastAimingStatus: 0
  IsChangeDirection: 0
  IsAutoAiming: 0
  IsDetectTarget: 0
  IsAutoShooting: 0
  IsShooting: 0
  <IsTargetBehind>k__BackingField: 0
  <WantsToRotateBehindTarget>k__BackingField: 0
  RunningFactor:
    EnableLearping: 0
    Timer: 0
    Value: 0
  IsStopping: 0
  InputMagnitude:
    EnableLearping: 0
    Timer: 0
    Value: 0
  StartWalkAngle:
    EnableLearping: 0
    Timer: 0
    Value: 0
  InputAngle:
    EnableLearping: 0
    Timer: 0.25
    Value: 0
  StopWalkAngle:
    EnableLearping: 0
    Timer: 0
    Value: 0
  CurrentPlayerSpeed:
    EnableLearping: 0
    Timer: 0
    Value: 0
  PlayerInputDirection:
    EnableLearping: 0
    Timer: 0.1
    Value: {x: 0, y: 0, z: 0}
  IsGrounded: 1
  IsLeftMoving: 0
  IsRightMoving: 0
  IsFwrdMoving: 0
  IsBackMoving: 0
  IsLeftFootstep: 0
  IsRightFootstep: 0
  WeaponIndex: 0
  AimTarget:
    x: 0
    y: 0
    z: 0
  PlayerTransform: {fileID: 8775929964290693195}
  <InputName>k__BackingField: 000000000100000002000000030000000400000005000000060000000700000008000000090000000a0000000b0000000c0000000d0000000e0000000f000000100000001100000012000000130000001400000015000000160000001700000018000000
--- !u!114 &202015275302094361
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 8b9a63380f5048a8a3159c7b9a9d9834, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  serializationData:
    SerializedFormat: 2
    SerializedBytes: 
    ReferencedUnityObjects: []
    SerializedBytesString: 
    Prefab: {fileID: 0}
    PrefabModificationsReferencedUnityObjects: []
    PrefabModifications: []
    SerializationNodes:
    - Name: subStates
      Entry: 6
      Data: 
    - Name: subModules
      Entry: 6
      Data: 
    - Name: statesHolder
      Entry: 6
      Data: 
  currentMainState: 0
--- !u!114 &9111336882492878132
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f56769f332de420e9a8d8b1ecc602b0f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _puppetMaster: {fileID: 7905871042871541643}
  layers:
    serializedVersion: 2
    m_Bits: 0
  unpin: 1
  force: 1
--- !u!114 &746262152108682085
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 0816b76efdaf0f8499691c5103211fc5, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _AnimationManager: {fileID: 4937370481206954850}
  m_currentUpperBodyAnimation:
    _FadeDuration: 0.25
    _Events:
      _NormalizedTimes: []
      _Callbacks: []
      _Names: []
    _Clip: {fileID: 0}
    _Speed: 1
    _NormalizedStartTime: NaN
--- !u!114 &4937370481206954850
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ab8cfa865e8923b45b97f2e6e5d21ce9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  _Animancer: {fileID: 9096211446228400863}
  _ActionMask: {fileID: 31900000, guid: 2ee86d3a5e2d3cf4fa965c906da1eb50, type: 2}
  _ActionFadeDuration: 0.25
--- !u!114 &5212865479793037668
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8786989549537887712}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 01f39886f6899de409e6a9c737a8a236, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  showDetectionRadius: 0
  detectionRadiusColor: {r: 0.2, g: 0.8, b: 0.2, a: 1}
  detectionAngleColor: {r: 0.8, g: 0.8, b: 0.2, a: 1}
  configurationRadiusColor: {r: 1, g: 0.5, b: 0, a: 1}
  detectedEnemiesCount: 0
  enemiesWithHealthBarCount: 0
--- !u!1 &8851145878541517776
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 6990328291944331678}
  - component: {fileID: 5045617498075551410}
  m_Layer: 0
  m_Name: CC_Base_Eye
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &6990328291944331678
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8851145878541517776}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &5045617498075551410
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8851145878541517776}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 98a9f2539d1d803429e41de3b5eb3aaa, type: 2}
  - {fileID: 2100000, guid: 7ed162a0af41f734da1e1bc7234e14db, type: 2}
  - {fileID: 2100000, guid: 37e5e6234a4b1724fb93f8ec91aa3422, type: 2}
  - {fileID: 2100000, guid: f07afddac0c44864da4a903f3da211a4, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: -7654024864961597166, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 3686526413400921503}
  - {fileID: 1944894567241051680}
  m_BlendShapeWeights:
  - 0
  - 0
  m_RootBone: {fileID: 1944894567241051680}
  m_AABB:
    m_Center: {x: -0.031884596, y: -0.00096217636, z: -0.00013517216}
    m_Extent: {x: 0.048445284, y: 0.01696821, z: 0.01673663}
  m_DirtyAABB: 0
--- !u!1 &8926179142097197875
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2332395235700116694}
  - component: {fileID: 137225865744263422}
  - component: {fileID: 8577888195720493910}
  m_Layer: 8
  m_Name: QuadTextured
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2332395235700116694
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8926179142097197875}
  serializedVersion: 2
  m_LocalRotation: {x: 0.7099969, y: 0, z: 0, w: 0.70420486}
  m_LocalPosition: {x: 0, y: 0.12, z: -0.02156341}
  m_LocalScale: {x: 0.5, y: 0.50000006, z: 0.50000006}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 5466703835676033078}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!33 &137225865744263422
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8926179142097197875}
  m_Mesh: {fileID: 10210, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &8577888195720493910
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8926179142097197875}
  m_Enabled: 0
  m_CastShadows: 0
  m_ReceiveShadows: 0
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 0
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 0}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 0
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!1 &8945726965302419653
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 7001565842920156955}
  - component: {fileID: 1904154745678220519}
  m_Layer: 0
  m_Name: CC_Base_Tongue
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 0
--- !u!4 &7001565842920156955
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8945726965302419653}
  serializedVersion: 2
  m_LocalRotation: {x: -0.7071068, y: 0, z: -0, w: 0.7071068}
  m_LocalPosition: {x: -0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 8775929964290693195}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!137 &1904154745678220519
SkinnedMeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 8945726965302419653}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 3
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 442f10a898dd8ab45a79fe09d30c1443, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  serializedVersion: 2
  m_Quality: 0
  m_UpdateWhenOffscreen: 0
  m_SkinnedMotionVectors: 1
  m_Mesh: {fileID: 1654403436102926152, guid: aaae4fd01710c094e8577e15c1cde487, type: 3}
  m_Bones:
  - {fileID: 4657406374714801801}
  - {fileID: 5721097200813384516}
  - {fileID: 3180524282443571956}
  m_BlendShapeWeights:
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  - 0
  m_RootBone: {fileID: 3180524282443571956}
  m_AABB:
    m_Center: {x: -0.03286753, y: 0.003165951, z: -0.00006536394}
    m_Extent: {x: 0.054333396, y: 0.029733827, z: 0.025414914}
  m_DirtyAABB: 0
--- !u!1 &9009554111302687015
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1871936065825758296}
  m_Layer: 0
  m_Name: thigh_twist_01_r
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1871936065825758296
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9009554111302687015}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00000012267265, y: -0.0000017183896, z: 0.00000011269007,
    w: 1}
  m_LocalPosition: {x: 0.00000012397766, y: 0.2396434, z: -0.00000011920929}
  m_LocalScale: {x: 0.99999994, y: 0.99999994, z: 0.99999994}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 2009526134938540747}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9026852200598845695
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2381486250098498953}
  m_Layer: 8
  m_Name: mag
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &2381486250098498953
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9026852200598845695}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: -0, y: 0.019175243, z: -0.047334816}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 306422539164200583}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9076264202562572853
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1791615436228211716}
  m_Layer: 8
  m_Name: VaultDirBase
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1791615436228211716
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9076264202562572853}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0.99771255, z: 0, w: 0.06759941}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 0.25699458, y: 0.2569946, z: 0.25699458}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 7237376372815333097}
  m_Father: {fileID: 2837586148865164468}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &9082943152550685825
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 3180524282443571956}
  m_Layer: 0
  m_Name: CC_Base_Tongue01
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &3180524282443571956
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 9082943152550685825}
  serializedVersion: 2
  m_LocalRotation: {x: 0.00029571867, y: 0.006775168, z: -0.11056743, w: 0.9938455}
  m_LocalPosition: {x: -0.02315749, y: 0.011044769, z: -0.00015940386}
  m_LocalScale: {x: 1, y: 1.0000001, z: 1.0000001}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 4657406374714801801}
  m_Father: {fileID: 9130952369729836117}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &8230633645950235272
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 306422539164200583}
    m_Modifications:
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.x
      value: 0.001
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.y
      value: 0.0443
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalPosition.z
      value: 0.1122
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.w
      value: -0.99732536
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.x
      value: 0.043009184
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.y
      value: -0.0132904295
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalRotation.z
      value: -0.05758212
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 90
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 180
      objectReference: {fileID: 0}
    - target: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Name
      value: AimPointer
      objectReference: {fileID: 0}
    - target: {fileID: 6508003453768678230, guid: 2c513e3cb59a72543b45a4c740779f56,
        type: 3}
      propertyPath: m_Layer
      value: 8
      objectReference: {fileID: 0}
    m_RemovedComponents: []
    m_RemovedGameObjects: []
    m_AddedGameObjects: []
    m_AddedComponents: []
  m_SourcePrefab: {fileID: 100100000, guid: 2c513e3cb59a72543b45a4c740779f56, type: 3}
--- !u!4 &8475396748067272981 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 551618117683448733, guid: 2c513e3cb59a72543b45a4c740779f56,
    type: 3}
  m_PrefabInstance: {fileID: 8230633645950235272}
  m_PrefabAsset: {fileID: 0}
