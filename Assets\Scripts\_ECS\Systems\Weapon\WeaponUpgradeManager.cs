using System.Collections.Generic;
using System.Linq;
using UnityEngine;
using PlayerFAP.Components.Weapon;
using PlayerFAP.ScriptableObjects;
using Unity.Entities;
using System;
using DefaultNamespace.Mono.Interface;
using Module.Weapon;
using Unity.Collections;
using Unity.Scenes;

namespace PlayerFAP.Systems.Weapon
{
    public class WeaponUpgradeManager : MonoBehaviour
    {
        private static WeaponUpgradeManager _instance;

        public static WeaponUpgradeManager Instance
        {
            get
            {
                if (_instance == null)
                {
                    _instance = FindFirstObjectByType<WeaponUpgradeManager>();
                    if (_instance == null)
                    {
                        Debug.LogError("WeaponUpgradeManager not found in scene!");
                    }
                }

                return _instance;
            }
        }

        [Header("Runtime Updates")] [Tooltip("How often to check for and apply config updates (in seconds)")]
        public float updateInterval = 0.5f;

        private float updateTimer = 0f;

        [Header("Configurations")] public List<WeaponConfig> availableWeapons = new List<WeaponConfig>();
        public List<BulletConfig> availableBullets = new List<BulletConfig>();

        [Header("Current Levels")] [SerializeField]
        private Dictionary<WeaponSubModuleState, int> weaponLevels = new Dictionary<WeaponSubModuleState, int>();

        [SerializeField] private Dictionary<BulletType, int> bulletLevels = new Dictionary<BulletType, int>();

        private EntityManager entityManager;
        private Entity weaponEntity;

        private void Awake()
        {
            if (_instance != null && _instance != this)
            {
                Destroy(gameObject);
                return;
            }

            _instance = this;

            // Initialize dictionaries
            foreach (var weapon in availableWeapons)
            {
                if (!weaponLevels.ContainsKey(weapon.weaponType))
                {
                    weaponLevels[weapon.weaponType] = 1; // Start at level 1
                }
            }

            foreach (var bullet in availableBullets)
            {
                if (!bulletLevels.ContainsKey(bullet.bulletType))
                {
                    bulletLevels[bullet.bulletType] = 1; // Start at level 1
                }
            }
        }

        private void Start()
        {
            entityManager = World.DefaultGameObjectInjectionWorld.EntityManager;
            TryFindWeaponEntity();

            // Log available bullet configurations
            DebugLogManager.Instance.Log($"WeaponUpgradeManager initialized with {availableBullets.Count} bullet configurations:");
            foreach (var bullet in availableBullets)
            {
                if (bullet != null)
                {
                    DebugLogManager.Instance.Log(
                        $"- {bullet.bulletName} (Type: {bullet.bulletType}), Base Speed: {bullet.baseStats.speed}, Upgrades: {bullet.upgradeLevels.Count}");
                }
                else
                {
                    Debug.LogError("Null bullet configuration found in availableBullets list!");
                }
            }

            // Log available weapon configurations
            DebugLogManager.Instance.Log($"WeaponUpgradeManager initialized with {availableWeapons.Count} weapon configurations:");
            foreach (var weapon in availableWeapons)
            {
                if (weapon != null)
                {
                    DebugLogManager.Instance.Log($"- {weapon.weaponName} (Type: {weapon.weaponType}), " +
                              $"Base FireRate: {weapon.baseStats.fireRate}, " +
                              $"Base Accuracy: {weapon.baseStats.accuracy}, " +
                              $"Base MaxRange: {weapon.baseStats.maxRange}, " +
                              $"Base DamageAmount: {weapon.baseStats.damageAmount}, " +
                              $"Upgrades: {weapon.upgradeLevels.Count}");
                }
                else
                {
                    Debug.LogError("Null weapon configuration found in availableWeapons list!");
                }
            }

            // Apply initial configurations
            //ApplyAllConfigurations();
        }

        private void Update()
        {
            // Periodically update all configurations
            updateTimer += Time.deltaTime;
            if (updateTimer >= updateInterval)
            {
                updateTimer = 0f;
                ApplyAllConfigurations();
            }
        }

        private bool TryFindWeaponEntity()
        {
            if (entityManager == null || !World.DefaultGameObjectInjectionWorld.IsCreated)
                return false;

            var weaponQuery = entityManager.CreateEntityQuery(
                ComponentType.ReadWrite<ShootingComponent>(),
                ComponentType.ReadWrite<WeaponStateComponent>());

            if (!weaponQuery.IsEmpty)
            {
                try
                {
                    weaponEntity = weaponQuery.GetSingletonEntity();
                    return entityManager.Exists(weaponEntity);
                }
                catch (Exception e)
                {
                    Debug.LogWarning($"Error finding weapon entity: {e.Message}");
                    return false;
                }
            }

            return false;
        }

        // Upgrade a weapon to the next level
        public bool UpgradeWeapon(WeaponSubModuleState weaponType)
        {
            if (!weaponLevels.ContainsKey(weaponType))
            {
                Debug.LogWarning($"Weapon type {weaponType} not found in available weapons!");
                return false;
            }

            // Find the weapon config
            WeaponConfig config = availableWeapons.Find(w => w.weaponType == weaponType);
            if (config == null)
            {
                Debug.LogWarning($"No configuration found for weapon type {weaponType}!");
                return false;
            }

            // Check if we can upgrade further
            int currentLevel = weaponLevels[weaponType];
            if (currentLevel >= config.upgradeLevels.Count)
            {
                DebugLogManager.Instance.Log($"Weapon {weaponType} is already at maximum level ({currentLevel})!");
                return false;
            }

            // Upgrade the weapon
            weaponLevels[weaponType] = currentLevel + 1;

            // Apply the upgrade to the weapon entity if it exists
            if (weaponEntity != Entity.Null && entityManager != null && entityManager.Exists(weaponEntity))
            {
                ApplyWeaponUpgrade(weaponType);
            }

            DebugLogManager.Instance.Log($"Upgraded {weaponType} to level {weaponLevels[weaponType]}!");
            return true;
        }

        // Upgrade a bullet to the next level
        public bool UpgradeBullet(BulletType bulletType)
        {
            if (!bulletLevels.ContainsKey(bulletType))
            {
                Debug.LogWarning($"Bullet type {bulletType} not found in available bullets!");
                return false;
            }

            // Find the bullet config
            BulletConfig config = availableBullets.Find(b => b.bulletType == bulletType);
            if (config == null)
            {
                Debug.LogWarning($"No configuration found for bullet type {bulletType}!");
                return false;
            }

            // Check if we can upgrade further
            int currentLevel = bulletLevels[bulletType];
            if (currentLevel >= config.upgradeLevels.Count)
            {
                DebugLogManager.Instance.Log($"Bullet {bulletType} is already at maximum level ({currentLevel})!");
                return false;
            }

            // Upgrade the bullet
            bulletLevels[bulletType] = currentLevel + 1;

            DebugLogManager.Instance.Log($"Upgraded {bulletType} bullet to level {bulletLevels[bulletType]}!");
            return true;
        }

        // Apply all configurations to all entities
        public void ApplyAllConfigurations()
        {
            // Make sure we have a valid weapon entity
            if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
            {
                if (!TryFindWeaponEntity())
                {
                    Debug.LogWarning("Cannot apply configurations - weapon entity not found!");
                    return;
                }
            }

            // Apply weapon configurations
            foreach (var weaponConfig in availableWeapons)
            {
                ApplyWeaponUpgrade(weaponConfig.weaponType);
            }

            // Apply bullet configurations to all active bullets
            ApplyBulletConfigurations();

            // Update bullet prefabs in the weapon entity
            UpdateBulletPrefabs();

            DebugLogManager.Instance.Log("Applied all weapon and bullet configurations from config files");
        }

        // Update bullet prefabs in the weapon entity
        private void UpdateBulletPrefabs()
        {
            if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
            {
                Debug.LogWarning("Cannot update bullet prefabs - weapon entity not found!");
                return;
            }

            if (!entityManager.HasBuffer<BulletPrefab>(weaponEntity))
            {
                Debug.LogWarning("Weapon entity does not have a BulletPrefab buffer!");
                return;
            }

            DebugLogManager.Instance.Log("Updating bullet prefabs in weapon entity");

            // Get the bullet prefab buffer
            var prefabBuffer = entityManager.GetBuffer<BulletPrefab>(weaponEntity);

            // Log the current prefabs
            DebugLogManager.Instance.Log($"Weapon entity has {prefabBuffer.Length} bullet prefabs:");
            for (int i = 0; i < prefabBuffer.Length; i++)
            {
                var prefab = prefabBuffer[i];
                DebugLogManager.Instance.Log($"- Prefab {i}: Type={prefab.BulletType}, Entity={prefab.PrefabEntity.Index}");

                // Check if the entity exists
                if (!entityManager.Exists(prefab.PrefabEntity))
                {
                    Debug.LogWarning($"Bullet prefab entity {prefab.PrefabEntity.Index} does not exist!");
                    continue;
                }

                // Check if it has a BulletComponent
                if (!entityManager.HasComponent<BulletComponent>(prefab.PrefabEntity))
                {
                    Debug.LogWarning(
                        $"Bullet prefab entity {prefab.PrefabEntity.Index} does not have a BulletComponent!");
                    continue;
                }

                // Get the current bullet component
                var bulletComponent = entityManager.GetComponentData<BulletComponent>(prefab.PrefabEntity);

                // Find the bullet config for this bullet type
                BulletConfig config = availableBullets.Find(b => b.bulletType == prefab.BulletType);
                if (config == null)
                {
                    Debug.LogWarning($"No config found for bullet type {prefab.BulletType}!");
                    continue;
                }

                // Get the current level for this bullet type
                int level = GetBulletLevel(prefab.BulletType);
                BulletStats stats = config.GetStatsForLevel(level);

                // Update the bullet component with the new stats
                float oldSpeed = bulletComponent.Speed;
                bulletComponent.Speed = stats.speed;

                // Update other properties as needed
                bulletComponent.MaxDistance = stats.maxDistance;
                bulletComponent.CanPenetrate = stats.canPenetrate;
                bulletComponent.MaxPenetrations = stats.maxPenetrations;
                bulletComponent.PenetrationDamageReduction = stats.penetrationDamageReduction;
                bulletComponent.ExplodeOnImpact = stats.explodeOnImpact;
                bulletComponent.ExplosionRadius = stats.explosionRadius;
                bulletComponent.ExplosionDamageMultiplier = stats.explosionDamageMultiplier;

                // Apply the updated component
                entityManager.SetComponentData(prefab.PrefabEntity, bulletComponent);

                //DebugLogManager.Instance.Log($"Updated bullet prefab {prefab.PrefabEntity.Index} of type {prefab.BulletType}: Speed {oldSpeed} -> {stats.speed}");
            }
        }

        // Apply weapon upgrade to the ECS entity
        private void ApplyWeaponUpgrade(WeaponSubModuleState weaponType)
        {
            if (weaponEntity == Entity.Null || !entityManager.Exists(weaponEntity))
            {
                if (!TryFindWeaponEntity())
                {
                    Debug.LogWarning("Cannot apply weapon upgrade - weapon entity not found!");
                    return;
                }
            }

            WeaponConfig config = availableWeapons.Find(w => w.weaponType == weaponType);
            if (config == null)
            {
                Debug.LogWarning(
                    $"No weapon config found for type {weaponType}. Available types: {string.Join(", ", availableWeapons.Select(w => w.weaponType.ToString()))}");
                return;
            }

            int level = GetWeaponLevel(weaponType);
            WeaponStats stats = config.GetStatsForLevel(level);

            DebugLogManager.Instance.Log($"Applying weapon upgrade for {config.weaponName} (Type: {weaponType}) at level {level}:");
            DebugLogManager.Instance.Log($"  Stats - FireRate: {stats.fireRate}, Accuracy: {stats.accuracy}, " +
                      $"MaxRange: {stats.maxRange}, DamageAmount: {stats.damageAmount}, " +
                      $"SpreadAngle: {stats.spreadAngle}, RecoilAmount: {stats.recoilAmount}, " +
                      $"RecoilRecovery: {stats.recoilRecovery}");

            // Update ShootingComponent
            if (entityManager.HasComponent<ShootingComponent>(weaponEntity))
            {
                var shootingComponent = entityManager.GetComponentData<ShootingComponent>(weaponEntity);

                // Log current values before update
                DebugLogManager.Instance.Log(
                    $"  ECS Entity Before - FireRate: {shootingComponent.FireRate}, Accuracy: {shootingComponent.Accuracy}, " +
                    $"MaxRange: {shootingComponent.MaxRange}, DamageAmount: {shootingComponent.DamageAmount}");

                shootingComponent.FireRate = stats.fireRate;
                shootingComponent.Accuracy = stats.accuracy;
                shootingComponent.MaxRange = stats.maxRange;
                shootingComponent.DamageAmount = stats.damageAmount;
                shootingComponent.SpreadAngle = stats.spreadAngle;
                shootingComponent.RecoilAmount = stats.recoilAmount;
                shootingComponent.RecoilRecovery = stats.recoilRecovery;
                entityManager.SetComponentData(weaponEntity, shootingComponent);

                // Log updated values
                DebugLogManager.Instance.Log(
                    $"  ECS Entity After - FireRate: {shootingComponent.FireRate}, Accuracy: {shootingComponent.Accuracy}, " +
                    $"MaxRange: {shootingComponent.MaxRange}, DamageAmount: {shootingComponent.DamageAmount}");
            }
            else
            {
                Debug.LogWarning($"Weapon entity does not have a ShootingComponent!");
            }

            // Update WeaponStateComponent
            if (entityManager.HasComponent<WeaponStateComponent>(weaponEntity))
            {
                var stateComponent = entityManager.GetComponentData<WeaponStateComponent>(weaponEntity);

                // Log current values before update
                DebugLogManager.Instance.Log(
                    $"  State Before - MaxAmmo: {stateComponent.MaxAmmo}, ReloadTime: {stateComponent.ReloadTime}, " +
                    $"WeaponLevel: {stateComponent.WeaponLevel}");

                stateComponent.MaxAmmo = config.GetMaxAmmoForLevel(level);
                stateComponent.ReloadTime = config.GetReloadTimeForLevel(level);
                stateComponent.WeaponType = weaponType;
                stateComponent.WeaponLevel = level;
                entityManager.SetComponentData(weaponEntity, stateComponent);

                // Log updated values
                DebugLogManager.Instance.Log(
                    $"  State After - MaxAmmo: {stateComponent.MaxAmmo}, ReloadTime: {stateComponent.ReloadTime}, " +
                    $"WeaponLevel: {stateComponent.WeaponLevel}");
            }
            else
            {
                Debug.LogWarning($"Weapon entity does not have a WeaponStateComponent!");
            }

            // Also update any WeaponSubModule instances in the scene
            UpdateWeaponSubModules(weaponType, stats);

            DebugLogManager.Instance.Log($"Applied configuration for {weaponType} weapon at level {level}");
        }

        // Update all WeaponSubModule instances in the scene
        private void UpdateWeaponSubModules(WeaponSubModuleState weaponType, WeaponStats stats)
        {
            // Find all WeaponSubModule instances in the scene
            RefactoredWeaponModule weaponModules = (RefactoredWeaponModule)GameManager.Instance.moduleManager.GetModule<RefactoredWeaponModule>();

            if(weaponModules == null) return;
            
            foreach (var weaponSubModule in weaponModules.SubModules)
            {
                // Log current values before update
                DebugLogManager.Instance.Log($"Updating WeaponSubModule {weaponSubModule.Value.name} (Type: {weaponType}):");
                DebugLogManager.Instance.Log(
                    $"  Before - FireRate: {weaponSubModule.Value.FireRate}, Accuracy: {weaponSubModule.Value.Accuracy}, " +
                    $"MaxRange: {weaponSubModule.Value.MaxRange}, DamageAmount: {weaponSubModule.Value.DamageAmount}");

                // Update weapon properties from config
                weaponSubModule.Value.FireRate = stats.fireRate;
                weaponSubModule.Value.Accuracy = stats.accuracy;
                weaponSubModule.Value.MaxRange = stats.maxRange;
                weaponSubModule.Value.DamageAmount = stats.damageAmount;
                weaponSubModule.Value.SpreadAngle = stats.spreadAngle;
                weaponSubModule.Value.RecoilAmount = stats.recoilAmount;
                weaponSubModule.Value.RecoilRecovery = stats.recoilRecovery;

                // Log updated values
                DebugLogManager.Instance.Log(
                    $"  After - FireRate: {weaponSubModule.Value.FireRate}, Accuracy: {weaponSubModule.Value.Accuracy}, " +
                    $"MaxRange: {weaponSubModule.Value.MaxRange}, DamageAmount: {weaponSubModule.Value.DamageAmount}");
            }
        }

        // Apply bullet configurations to all active bullets
        private void ApplyBulletConfigurations()
        {
            DebugLogManager.Instance.Log("ApplyBulletConfigurations called - updating all active bullets");

            // Query for all active bullets
            var bulletQuery = entityManager.CreateEntityQuery(ComponentType.ReadWrite<BulletComponent>());
            var bullets = bulletQuery.ToEntityArray(Allocator.Temp);

            try
            {
                DebugLogManager.Instance.Log($"Found {bullets.Length} active bullets to update");

                for (int i = 0; i < bullets.Length; i++)
                {
                    Entity bulletEntity = bullets[i];
                    if (!entityManager.Exists(bulletEntity))
                    {
                        Debug.LogWarning($"Bullet entity {bulletEntity.Index} no longer exists, skipping");
                        continue;
                    }

                    var bulletComponent = entityManager.GetComponentData<BulletComponent>(bulletEntity);
                    float oldSpeed = bulletComponent.Speed;

                    // Find the bullet config for this bullet type
                    BulletConfig config = null;
                    foreach (var bulletConfig in availableBullets)
                    {
                        if (bulletConfig.bulletType == bulletComponent.BulletType)
                        {
                            config = bulletConfig;
                            break;
                        }
                    }

                    if (config == null)
                    {
                        Debug.LogWarning($"No config found for bullet type {bulletComponent.BulletType}, skipping");
                        continue;
                    }

                    // Get the current level for this bullet type
                    int level = GetBulletLevel(bulletComponent.BulletType);
                    BulletStats stats = config.GetStatsForLevel(level);

                    // Update bullet properties from config
                    bulletComponent.Speed = stats.speed;
                    bulletComponent.Damage *= stats.damageMultiplier; // Apply multiplier to base damage
                    bulletComponent.MaxDistance = stats.maxDistance;

                    // Update special properties
                    bulletComponent.CanPenetrate = stats.canPenetrate;
                    bulletComponent.MaxPenetrations = stats.maxPenetrations;
                    bulletComponent.PenetrationDamageReduction = stats.penetrationDamageReduction;

                    bulletComponent.ExplodeOnImpact = stats.explodeOnImpact;
                    bulletComponent.ExplosionRadius = stats.explosionRadius;
                    bulletComponent.ExplosionDamageMultiplier = stats.explosionDamageMultiplier;

                    // Apply the updated component
                    entityManager.SetComponentData(bulletEntity, bulletComponent);

                    //DebugLogManager.Instance.Log($"Updated bullet {bulletEntity.Index} of type {bulletComponent.BulletType}: Speed {oldSpeed} -> {stats.speed}");
                }

                //DebugLogManager.Instance.Log($"Applied configurations to {bullets.Length} active bullets");
            }
            finally
            {
                // Always dispose of native arrays
                if (bullets.IsCreated)
                    bullets.Dispose();
            }
        }

        // Get current weapon level
        public int GetWeaponLevel(WeaponSubModuleState weaponType)
        {
            if (weaponLevels.TryGetValue(weaponType, out int level))
            {
                return level;
            }

            return 0;
        }

        // Get current bullet level
        public int GetBulletLevel(BulletType bulletType)
        {
            if (bulletLevels.TryGetValue(bulletType, out int level))
            {
                return level;
            }

            return 0;
        }

        // Get weapon stats for current level
        public WeaponStats GetCurrentWeaponStats(WeaponSubModuleState weaponType)
        {
            WeaponConfig config = availableWeapons.Find(w => w.weaponType == weaponType);
            if (config == null)
            {
                Debug.LogWarning(
                    $"No weapon config found for type {weaponType}. Available types: {string.Join(", ", availableWeapons.Select(w => w.weaponType.ToString()))}");
                return null;
            }

            int level = GetWeaponLevel(weaponType);
            WeaponStats stats = config.GetStatsForLevel(level);

            DebugLogManager.Instance.Log($"Retrieved weapon stats for {config.weaponName} (Type: {weaponType}) at level {level}: " +
                      $"FireRate={stats.fireRate}, Accuracy={stats.accuracy}, MaxRange={stats.maxRange}, " +
                      $"DamageAmount={stats.damageAmount}, SpreadAngle={stats.spreadAngle}, " +
                      $"RecoilAmount={stats.recoilAmount}, RecoilRecovery={stats.recoilRecovery}");

            return stats;
        }

        // Get bullet stats for current level
        public BulletStats GetCurrentBulletStats(BulletType bulletType)
        {
            BulletConfig config = availableBullets.Find(b => b.bulletType == bulletType);
            if (config == null)
            {
                Debug.LogWarning(
                    $"No bullet config found for type {bulletType}. Available types: {string.Join(", ", availableBullets.Select(b => b.bulletType.ToString()))}");
                return null;
            }

            int level = GetBulletLevel(bulletType);
            BulletStats stats = config.GetStatsForLevel(level);
            DebugLogManager.Instance.Log(
                $"Retrieved bullet stats for {config.bulletName} (Type: {bulletType}) at level {level}: Speed={stats.speed}");
            return stats;
        }

        // Create example configurations for testing
        [ContextMenu("Create Example Configs")]
        public void CreateExampleConfigs()
        {
            DebugLogManager.Instance.Log("Creating example weapon and bullet configurations...");
            // This would be implemented to create example configs in the editor
        }
    }
}