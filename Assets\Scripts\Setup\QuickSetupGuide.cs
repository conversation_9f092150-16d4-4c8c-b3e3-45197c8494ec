using UnityEngine;
using Sirenix.OdinInspector;
using Controllers;
using Module.Mono.Animancer.RealsticFemale;

namespace Setup
{
    /// <summary>
    /// Quick setup guide with visual hierarchy for edge case controllers
    /// </summary>
    public class QuickSetupGuide : MonoBehaviour
    {
        [InfoBox("Add this script to your Player GameObject and follow the steps below:", InfoMessageType.Info)]

        [InfoBox("The EdgeCaseControllerManager coordinates all edge case controllers and integrates with your existing MovementModule.", InfoMessageType.None)]
        [<PERSON><PERSON>("Add EdgeCaseControllerManager", ButtonSizes.Large)]
        [GUIColor(0.4f, 0.8f, 1f)]
        public void AddControllerManager()
        {
            var manager = GetComponent<EdgeCaseControllerManager>();
            if (manager == null)
            {
                gameObject.AddComponent<EdgeCaseControllerManager>();
                Debug.Log("✅ Added EdgeCaseControllerManager to " + gameObject.name);
            }
            else
            {
                Debug.Log("⚠️ EdgeCaseControllerManager already exists on " + gameObject.name);
            }
        }

        [InfoBox("These controllers fix specific edge cases:\n• UnifiedSpeedController: Fixes close enemy speed issues\n• EnhancedInputProcessor: Fixes virtual joystick glitches\n• AdvancedUpperBodyRotationController: Enhances rotation limits", InfoMessageType.None)]

        [Button("Add UnifiedSpeedController")]
        [GUIColor(0.8f, 1f, 0.4f)]
        public void AddSpeedController()
        {
            var controller = GetComponent<UnifiedSpeedController>();
            if (controller == null)
            {
                gameObject.AddComponent<UnifiedSpeedController>();
                Debug.Log("✅ Added UnifiedSpeedController - Fixes close enemy speed issues");
            }
            else
            {
                Debug.Log("⚠️ UnifiedSpeedController already exists");
            }
        }

        [Button("Add EnhancedInputProcessor")]
        [GUIColor(0.8f, 1f, 0.4f)]
        public void AddInputProcessor()
        {
            var processor = GetComponent<EnhancedInputProcessor>();
            if (processor == null)
            {
                gameObject.AddComponent<EnhancedInputProcessor>();
                Debug.Log("✅ Added EnhancedInputProcessor - Fixes virtual joystick glitches");
            }
            else
            {
                Debug.Log("⚠️ EnhancedInputProcessor already exists");
            }
        }

        [Button("Add AdvancedUpperBodyRotationController")]
        [GUIColor(0.8f, 1f, 0.4f)]
        public void AddRotationController()
        {
            var controller = GetComponent<AdvancedUpperBodyRotationController>();
            if (controller == null)
            {
                gameObject.AddComponent<AdvancedUpperBodyRotationController>();
                Debug.Log("✅ Added AdvancedUpperBodyRotationController - Enhances rotation limits");
            }
            else
            {
                Debug.Log("⚠️ AdvancedUpperBodyRotationController already exists");
            }
        }

        [Button("🚀 ADD ALL CONTROLLERS", ButtonSizes.Large)]
        [GUIColor(0.2f, 1f, 0.2f)]
        public void AddAllControllers()
        {
            AddControllerManager();
            AddSpeedController();
            AddInputProcessor();
            AddRotationController();

            Debug.Log("🎉 All edge case controllers added! Check the configuration below.");
        }

        [Header("📋 Step 3: Configure Controllers")]
        [InfoBox("After adding controllers, configure them in the Inspector. The most important settings are highlighted below.", InfoMessageType.None)]

        [Header("🎯 Required Configuration")]
        [SerializeField] private Transform spineRoot;
        [SerializeField] private Transform headTransform;
        [SerializeField] private float baseMovementSpeed = 5f;

        [Button("Auto-Configure Controllers")]
        [GUIColor(1f, 0.8f, 0.4f)]
        public void AutoConfigureControllers()
        {
            // Configure AdvancedUpperBodyRotationController
            var rotationController = GetComponent<AdvancedUpperBodyRotationController>();
            if (rotationController != null)
            {
                // Try to auto-find spine and head
                if (spineRoot == null)
                    spineRoot = FindTransformByName("Spine");
                if (headTransform == null)
                    headTransform = FindTransformByName("Head");

                // Configure player rotation settings
                rotationController.SetRotateEntirePlayer(true);
                rotationController.SetPlayerRotationThreshold(30f);

                Debug.Log($"🎯 Rotation Controller configured - Spine: {spineRoot?.name}, Head: {headTransform?.name}");
                Debug.Log($"🎯 Player rotation enabled with 30° threshold");
            }

            // Configure UnifiedSpeedController
            var speedController = GetComponent<UnifiedSpeedController>();
            if (speedController != null)
            {
                speedController.SetBaseMovementSpeed(baseMovementSpeed);
                Debug.Log($"🎯 Speed Controller configured - Base Speed: {baseMovementSpeed}");
            }

            // Configure EnhancedInputProcessor
            var inputProcessor = GetComponent<EnhancedInputProcessor>();
            if (inputProcessor != null)
            {
                inputProcessor.SetDeadzone(0.1f);
                inputProcessor.SetSensitivity(1.2f);
                Debug.Log("🎯 Input Processor configured - Deadzone: 0.1, Sensitivity: 1.2");
            }

            Debug.Log("✅ Auto-configuration complete!");
        }

        private Transform FindTransformByName(string name)
        {
            Transform[] allTransforms = GetComponentsInChildren<Transform>();
            foreach (Transform t in allTransforms)
            {
                if (t.name.ToLower().Contains(name.ToLower()))
                {
                    return t;
                }
            }
            return null;
        }

        [InfoBox("Test that the controllers are working correctly with your existing systems.", InfoMessageType.None)]

        [Button("🧪 Test All Controllers")]
        [GUIColor(0.6f, 0.8f, 1f)]
        public void TestAllControllers()
        {
            var manager = GetComponent<EdgeCaseControllerManager>();
            if (manager != null)
            {
                var status = manager.GetStatus();
                Debug.Log($"📊 Controller Manager Status: {status}");
            }

            var speedController = GetComponent<UnifiedSpeedController>();
            if (speedController != null)
            {
                var breakdown = speedController.GetSpeedBreakdown();
                Debug.Log($"📊 Speed Controller: {breakdown}");
            }

            var inputProcessor = GetComponent<EnhancedInputProcessor>();
            if (inputProcessor != null)
            {
                var stats = inputProcessor.GetProcessingStats();
                Debug.Log($"📊 Input Processor: {stats}");
            }

            var rotationController = GetComponent<AdvancedUpperBodyRotationController>();
            if (rotationController != null)
            {
                RotationStatusDebug rotationStatus = rotationController.GetRotationStatus();
                Debug.Log($"📊 Rotation Controller: {rotationStatus}");
            }

            Debug.Log("✅ Controller testing complete! Check console for detailed status.");
        }

        [InfoBox("This shows the current state of your Player GameObject hierarchy:", InfoMessageType.None)]

        [Button("📋 Show Current Hierarchy")]
        public void ShowCurrentHierarchy()
        {
            Debug.Log("🏗️ CURRENT PLAYER HIERARCHY:");
            Debug.Log($"Player GameObject: {gameObject.name}");

            var manager = GetComponent<EdgeCaseControllerManager>();
            Debug.Log($"├── EdgeCaseControllerManager: {(manager != null ? "✅ Added" : "❌ Missing")}");

            var speedController = GetComponent<UnifiedSpeedController>();
            Debug.Log($"├── UnifiedSpeedController: {(speedController != null ? "✅ Added" : "❌ Missing")}");

            var inputProcessor = GetComponent<EnhancedInputProcessor>();
            Debug.Log($"├── EnhancedInputProcessor: {(inputProcessor != null ? "✅ Added" : "❌ Missing")}");

            var rotationController = GetComponent<AdvancedUpperBodyRotationController>();
            Debug.Log($"├── AdvancedUpperBodyRotationController: {(rotationController != null ? "✅ Added" : "❌ Missing")}");



            Debug.Log("└── Other existing components...");

            if (manager != null && speedController != null && inputProcessor != null && rotationController != null)
            {
                Debug.Log("🎉 SETUP COMPLETE! All edge case controllers are ready.");
                Debug.Log("🎮 Your game now has:");
                Debug.Log("   • Fixed close enemy speed issues");
                Debug.Log("   • Fixed virtual joystick direction glitches");
                Debug.Log("   • Enhanced upper body rotation limits");
            }
            else
            {
                Debug.Log("⚠️ Setup incomplete. Use the buttons above to add missing controllers.");
            }
        }

        [InfoBox("After setup, you should see these improvements:\n• Smooth movement speed when close to enemies\n• No more virtual joystick direction glitches\n• Natural upper body rotation with dynamic limits\n• Better performance with 200+ enemies on mobile", InfoMessageType.Info)]

        private void Start()
        {
            ShowCurrentHierarchy();
        }
    }
}
