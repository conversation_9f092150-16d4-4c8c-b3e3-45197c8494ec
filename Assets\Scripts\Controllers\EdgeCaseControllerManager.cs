using UnityEngine;
using Sirenix.OdinInspector;
using Unity.Mathematics;
using Events;
using Controllers;

namespace Controllers
{
    /// <summary>
    /// Hierarchical manager for all edge case controllers
    /// Coordinates between MovementModule and the new controllers
    /// </summary>
    public class EdgeCaseControllerManager : MonoBehaviour
    {
        [Header("Controller References")]
        [SerializeField] private UnifiedSpeedController speedController;
        [SerializeField] private EnhancedInputProcessor inputProcessor;
        [SerializeField] private AdvancedUpperBodyRotationController rotationController;
        
        [Header("Integration Settings")]
        [SerializeField] private bool enableSpeedControllerIntegration = true;
        [SerializeField] private bool enableInputProcessorIntegration = true;
        [SerializeField] private bool enableRotationControllerIntegration = true;
        [SerializeField] private bool enableDebugLogging = false;
        
        [Header("Movement Module Integration")]
        [SerializeField] private bool overrideMovementModuleRotation = false;
        [SerializeField] private float rotationBlendFactor = 0.8f;
        [SerializeField] private float minRotationOverrideAngle = 15f;
        
        // State tracking
        private bool isInitialized = false;
        private Vector2 lastProcessedInput;
        private float lastCalculatedSpeed;
        private float lastRotationAngle;
        
        // Movement Module integration
        private Transform playerTransform;
        private float3 currentAimTarget;
        private bool isAiming;
        private bool isMoving;
        
        #region Unity Lifecycle
        
        private void Awake()
        {
            InitializeControllerManager();
        }
        
        private void Start()
        {
            SetupControllerReferences();
            SubscribeToEvents();
        }
        
        private void Update()
        {
            if (isInitialized)
            {
                UpdateControllerCoordination();
            }
        }
        
        private void OnDestroy()
        {
            UnsubscribeFromEvents();
        }
        
        #endregion
        
        #region Initialization
        
        /// <summary>
        /// Initialize the controller manager
        /// </summary>
        private void InitializeControllerManager()
        {
            playerTransform = transform;
            isInitialized = true;
            
            LogDebug("EdgeCaseControllerManager initialized");
        }
        
        /// <summary>
        /// Setup references to all controllers
        /// </summary>
        private void SetupControllerReferences()
        {
            // Get or create controllers
            if (speedController == null)
                speedController = GetComponent<UnifiedSpeedController>();
            
            if (inputProcessor == null)
                inputProcessor = GetComponent<EnhancedInputProcessor>();
            
            if (rotationController == null)
                rotationController = GetComponent<AdvancedUpperBodyRotationController>();
            
            LogDebug($"Controller references setup: Speed={speedController != null}, Input={inputProcessor != null}, Rotation={rotationController != null}");
        }
        
        #endregion
        
        #region Event Handling
        
        /// <summary>
        /// Subscribe to relevant events
        /// </summary>
        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Subscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnChangeDirectionEvent>(OnDirectionChanged);
            
            LogDebug("Subscribed to controller coordination events");
        }
        
        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnAimingOnTargetEvent>(OnAimingStarted);
            EventManager.Unsubscribe<OnUnAimingTargetEvent>(OnAimingStopped);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnChangeDirectionEvent>(OnDirectionChanged);
            
            LogDebug("Unsubscribed from controller coordination events");
        }
        
        /// <summary>
        /// Handle aiming started event
        /// </summary>
        private void OnAimingStarted(OnAimingOnTargetEvent eventData)
        {
            isAiming = true;
            currentAimTarget = eventData.TargetPosition;
            
            // Coordinate all controllers for aiming state
            CoordinateControllersForAiming(true);
            
            LogDebug($"Aiming started - coordinating controllers for target: {currentAimTarget}");
        }
        
        /// <summary>
        /// Handle aiming stopped event
        /// </summary>
        private void OnAimingStopped(OnUnAimingTargetEvent eventData)
        {
            isAiming = false;
            currentAimTarget = float3.zero;
            
            // Coordinate all controllers for normal state
            CoordinateControllersForAiming(false);
            
            LogDebug("Aiming stopped - coordinating controllers for normal movement");
        }
        
        /// <summary>
        /// Handle target detection event
        /// </summary>
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            currentAimTarget = eventData.TargetPosition;
            
            // Update all controllers with new target
            UpdateControllersWithTarget(eventData.TargetPosition);
            
            LogDebug($"Target detected - updating controllers: {eventData.TargetPosition}");
        }
        
        /// <summary>
        /// Handle target lost event
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            currentAimTarget = float3.zero;
            
            // Reset controllers to no-target state
            UpdateControllersWithTarget(float3.zero);
            
            LogDebug("Target lost - resetting controllers");
        }
        
        /// <summary>
        /// Handle direction change event
        /// </summary>
        private void OnDirectionChanged(OnChangeDirectionEvent eventData)
        {
            // Coordinate controllers for direction change
            CoordinateControllersForDirectionChange(eventData.IsAimToNormal);
            
            LogDebug($"Direction change - coordinating controllers: IsAimToNormal={eventData.IsAimToNormal}");
        }
        
        #endregion
        
        #region Controller Coordination
        
        /// <summary>
        /// Update controller coordination each frame
        /// </summary>
        private void UpdateControllerCoordination()
        {
            // Update movement state
            UpdateMovementState();
            
            // Coordinate input processing
            if (enableInputProcessorIntegration && inputProcessor != null)
            {
                CoordinateInputProcessing();
            }
            
            // Coordinate speed calculation
            if (enableSpeedControllerIntegration && speedController != null)
            {
                CoordinateSpeedCalculation();
            }
            
            // Coordinate rotation control
            if (enableRotationControllerIntegration && rotationController != null)
            {
                CoordinateRotationControl();
            }
        }
        
        /// <summary>
        /// Update movement state tracking
        /// </summary>
        private void UpdateMovementState()
        {
            // This would typically come from CharacterParameters or MovementModule
            // For now, we'll estimate based on input and speed
            isMoving = lastProcessedInput.magnitude > 0.1f && lastCalculatedSpeed > 0.1f;
        }
        
        /// <summary>
        /// Coordinate input processing
        /// </summary>
        private void CoordinateInputProcessing()
        {
            // Input processor handles its own processing
            // We just track the results for other controllers
            lastProcessedInput = inputProcessor.GetProcessedInput();
        }
        
        /// <summary>
        /// Coordinate speed calculation
        /// </summary>
        private void CoordinateSpeedCalculation()
        {
            // Update speed controller with current context
            speedController.UpdateMovementInput(lastProcessedInput);
            
            // Get calculated speed
            lastCalculatedSpeed = speedController.GetCurrentSpeed();
            
            // Update rotation controller with movement speed
            if (rotationController != null)
            {
                rotationController.SetMovementSpeed(lastCalculatedSpeed);
            }
        }
        
        /// <summary>
        /// Coordinate rotation control
        /// </summary>
        private void CoordinateRotationControl()
        {
            // Update rotation controller with current target
            if (!currentAimTarget.Equals(float3.zero))
            {
                rotationController.SetTargetPosition(currentAimTarget);
            }
            
            // Get current rotation angle
            lastRotationAngle = rotationController.GetCurrentRotationAngle();
        }
        
        /// <summary>
        /// Coordinate controllers for aiming state
        /// </summary>
        private void CoordinateControllersForAiming(bool isAiming)
        {
            // All controllers automatically receive aiming events
            // This method can be used for additional coordination if needed
            
            if (enableDebugLogging)
            {
                LogDebug($"Coordinated controllers for aiming state: {isAiming}");
            }
        }
        
        /// <summary>
        /// Update all controllers with target position
        /// </summary>
        private void UpdateControllersWithTarget(float3 targetPosition)
        {
            if (rotationController != null)
            {
                rotationController.SetTargetPosition(targetPosition);
            }
            
            // Speed controller automatically receives target events
            // Input processor doesn't need target information
        }
        
        /// <summary>
        /// Coordinate controllers for direction change
        /// </summary>
        private void CoordinateControllersForDirectionChange(bool isAimToNormal)
        {
            // Controllers handle direction changes through their own event subscriptions
            // This method can be used for additional coordination if needed
            
            if (enableDebugLogging)
            {
                LogDebug($"Coordinated controllers for direction change: {isAimToNormal}");
            }
        }
        
        #endregion
        
        #region MovementModule Integration
        
        /// <summary>
        /// Get processed input for MovementModule
        /// Call this from MovementModule.ApplyMovement
        /// </summary>
        public Vector2 GetProcessedInput(Vector2 rawInput)
        {
            if (enableInputProcessorIntegration && inputProcessor != null)
            {
                inputProcessor.SetRawInput(rawInput);
                return inputProcessor.GetProcessedInput();
            }
            
            return rawInput;
        }
        
        /// <summary>
        /// Get calculated movement speed for MovementModule
        /// Call this from MovementModule.ApplyMovement
        /// </summary>
        public float GetCalculatedSpeed(float baseSpeed)
        {
            if (enableSpeedControllerIntegration && speedController != null)
            {
                return speedController.GetCurrentSpeed();
            }
            
            return baseSpeed;
        }
        
        /// <summary>
        /// Check if rotation should be overridden by controllers
        /// Call this from MovementModule.UpdateCharacterRotation
        /// </summary>
        public bool ShouldOverrideRotation(float angleDifference)
        {
            if (!enableRotationControllerIntegration || !overrideMovementModuleRotation)
                return false;
            
            if (rotationController == null)
                return false;
            
            // Override rotation if angle difference is small enough for upper body rotation
            return Mathf.Abs(angleDifference) < minRotationOverrideAngle;
        }
        
        /// <summary>
        /// Get blended rotation for MovementModule
        /// Call this when ShouldOverrideRotation returns true
        /// </summary>
        public float GetBlendedRotation(float movementModuleAngle, float targetAngle)
        {
            if (rotationController == null)
                return movementModuleAngle;
            
            float controllerAngle = rotationController.GetCurrentRotationAngle();
            
            // Blend between movement module rotation and controller rotation
            return Mathf.LerpAngle(movementModuleAngle, targetAngle + controllerAngle, rotationBlendFactor);
        }
        
        #endregion
        
        #region Public API
        
        /// <summary>
        /// Get current controller status
        /// </summary>
        public ControllerManagerStatus GetStatus()
        {
            return new ControllerManagerStatus
            {
                IsInitialized = isInitialized,
                SpeedControllerActive = speedController != null && enableSpeedControllerIntegration,
                InputProcessorActive = inputProcessor != null && enableInputProcessorIntegration,
                RotationControllerActive = rotationController != null && enableRotationControllerIntegration,
                CurrentSpeed = lastCalculatedSpeed,
                ProcessedInput = lastProcessedInput,
                RotationAngle = lastRotationAngle,
                IsAiming = isAiming,
                IsMoving = isMoving
            };
        }
        
        /// <summary>
        /// Force update all controllers
        /// </summary>
        public void ForceUpdateControllers()
        {
            speedController?.ForceSpeedUpdate();
            inputProcessor?.ForceProcessInput();
            rotationController?.ForceUpdate();
            
            LogDebug("Forced update of all controllers");
        }
        
        /// <summary>
        /// Reset all controllers
        /// </summary>
        public void ResetAllControllers()
        {
            inputProcessor?.ResetInputState();
            rotationController?.ResetToNeutral();
            // Speed controller resets automatically
            
            LogDebug("Reset all controllers");
        }
        
        #endregion
        
        #region Utility Methods
        
        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[EdgeCaseControllerManager] {message}");
            }
        }
        
        #endregion
    }
    
    /// <summary>
    /// Controller manager status for debugging
    /// </summary>
    [System.Serializable]
    public struct ControllerManagerStatus
    {
        public bool IsInitialized;
        public bool SpeedControllerActive;
        public bool InputProcessorActive;
        public bool RotationControllerActive;
        public float CurrentSpeed;
        public Vector2 ProcessedInput;
        public float RotationAngle;
        public bool IsAiming;
        public bool IsMoving;
        
        public override string ToString()
        {
            return $"Manager Status: Init={IsInitialized}, Speed={SpeedControllerActive}, Input={InputProcessorActive}, " +
                   $"Rotation={RotationControllerActive}, CurrentSpeed={CurrentSpeed:F2}, Aiming={IsAiming}, Moving={IsMoving}";
        }
    }
}
