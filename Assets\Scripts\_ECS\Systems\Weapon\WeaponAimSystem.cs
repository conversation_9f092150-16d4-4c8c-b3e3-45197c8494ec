using DefaultNamespace.Mono.Interface;
using Module.Weapon;
using Unity.Entities;
using PlayerFAP.Components.Weapon;
using PlayerFAP.Components.Player;
using PlayerFAP.Tags;
using Unity.Collections;
using Unity.Mathematics;
using Unity.Transforms;
using UnityEngine;

namespace PlayerFAP.Systems.Weapon
{
    [UpdateInGroup(typeof(SimulationSystemGroup))]
    [UpdateBefore(typeof(OptimizedShootingSystem))]
    public partial class WeaponAimSystem : SystemBase
    {
        private EntityQuery weaponQuery;
        private EntityQuery playerQuery;
        private RefactoredWeaponModule weaponModule;

        protected override void OnCreate()
        {
            weaponQuery = GetEntityQuery(new EntityQueryBuilder(Allocator.Temp)
                .WithAll<WeaponStateComponent>());
                
            playerQuery = GetEntityQuery(new EntityQueryBuilder(Allocator.Temp)
                .WithAll<PlayerTag, LocalToWorld>());
        }

        protected override void OnStartRunning()
        {
        }

        protected override void OnUpdate()
        {
            if (weaponQuery.IsEmpty || weaponModule == null || playerQuery.IsEmpty)
                return;

            if (weaponModule == null)
                return;

            // // Get weapon position from the module (this should be under right hand bone)
            // var weaponPosition = weaponModule.GetCurrentAimPointer;
            //
            // // Update all weapon states with the weapon position
            // Entities
            //     .WithAll<WeaponStateComponent>()
            //     .ForEach((ref WeaponStateComponent weaponState) =>
            //     {
            //         weaponState.AimPointer = weaponPosition;
            //     })
            //     .Schedule();
        }
    }
}
