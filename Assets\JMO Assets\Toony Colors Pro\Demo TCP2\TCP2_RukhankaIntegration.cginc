/// TCP2_RukhankaIntegration.cginc
/// Rukhanka URP Deformation integration for TCP2 shaders using code injection.
/// See Rukhanka_URP_Shader_Integration_Guide.md for details.

//====================================================================
//# BLOCK: Rukhanka Deformation Properties
//# Inject @ Properties/Start
// Rukhanka mesh deformation properties
[HideInInspector]_DeformedMeshIndex("Deformed Mesh Buffer Index Offset", Float) = 0
[HideInInspector]_DeformationParamsForMotionVectors("Deformation Parameters", Vector) = (0,0,0,0)
[TCP2Separator]

//====================================================================
//# BLOCK: Rukhanka CBUFFER Variables
//# Inject @ Variables/Inside CBuffer
// NOTE: Do NOT redeclare Rukhanka properties in CBUFFER (URP handles this)
// (Leave empty unless you need to add custom CBUFFER vars)

//====================================================================
//# BLOCK: Rukhanka Shader Pragmas
//# Inject @ Main Pass/Pragma
#pragma multi_compile _ DOTS_INSTANCING_ON
#pragma target 4.5

//====================================================================
//# BLOCK: Rukhanka Includes
//# Inject @ Main Pass/HLSL Includes
#include "Packages/com.unity.render-pipelines.universal/ShaderLibrary/Core.hlsl"
#if defined(DOTS_INSTANCING_ON)
    UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
        UNITY_DOTS_INSTANCED_PROP_OVERRIDE_SUPPORTED(float, _DeformedMeshIndex)
        UNITY_DOTS_INSTANCED_PROP_OVERRIDE_SUPPORTED(float4, _DeformationParamsForMotionVectors)
    UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
#endif
#include "Packages/com.rukhanka.animation/Rukhanka.Runtime/Deformation/Resources/ComputeDeformedVertex.hlsl"

//====================================================================
//# BLOCK: Rukhanka Vertex Input Struct
//# Inject @ Main Pass/Vertex Input Struct/End
// Add vertexID for deformation support
uint vertexID : SV_VertexID;
UNITY_VERTEX_INPUT_INSTANCE_ID

//====================================================================
//# BLOCK: Rukhanka Vertex Deformation
//# Inject @ Main Pass/Vertex Function/Start
// Deform vertex using Rukhanka
float3 deformedPositionOS, deformedNormalOS, deformedTangentOS;
ComputeDeformedVertex_float(IN.vertexID, IN.positionOS.xyz, IN.normalOS, IN.tangentOS.xyz, deformedPositionOS, deformedNormalOS, deformedTangentOS);
// Use deformedPositionOS, deformedNormalOS, deformedTangentOS for subsequent transforms
// (Replace usages of IN.positionOS, IN.normalOS, IN.tangentOS as needed)

//====================================================================

//# BLOCK: Rukhanka DOTS Instanced Matrix Fix
//# REPLACE: UNITY_MATRIX_M
//# WITH:
#if defined(DOTS_INSTANCING_ON)
    float4x4 objectToWorld = ComputeDeformedObjectToWorldMatrix(IN.vertexID);
#else
    float4x4 objectToWorld = UNITY_MATRIX_M;
#endif
// Use objectToWorld instead of UNITY_MATRIX_M

// End of TCP2_RukhankaIntegration.cginc
