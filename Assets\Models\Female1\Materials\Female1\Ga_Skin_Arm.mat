%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &-8274537410206529065
MonoBehaviour:
  m_ObjectHideFlags: 11
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: d0353a89b1f911e48b9e16bdc9f2e058, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  version: 9
--- !u!21 &2100000
Material:
  serializedVersion: 8
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_Name: Ga_Skin_Arm
  m_Shader: {fileID: -6465566751694194690, guid: f56902d86aae325429ba35c2b665a021,
    type: 3}
  m_Parent: {fileID: 0}
  m_ModifiedSerializedProperties: 0
  m_ValidKeywords: []
  m_InvalidKeywords: []
  m_LightmapFlags: 4
  m_EnableInstancingVariants: 0
  m_DoubleSidedGI: 0
  m_CustomRenderQueue: -1
  stringTagMap: {}
  disabledShaderPasses:
  - MOTIONVECTORS
  m_LockedProperties: 
  m_SavedProperties:
    serializedVersion: 3
    m_TexEnvs:
    - _AnimatedBoneMatrices:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BaseMap:
        m_Texture: {fileID: 2800000, guid: e363ff84cb63129489cfbc9a3419e4be, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _BumpMap:
        m_Texture: {fileID: 2800000, guid: 889b2a27178ccda4383b5237e54671e1, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailMask:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _DetailNormalMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 20, y: 20}
        m_Offset: {x: 0, y: 0}
    - _EmissionMap:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MainTex:
        m_Texture: {fileID: 2800000, guid: a638d018027b041429fe2f06b313afa8, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _MetallicGlossMap:
        m_Texture: {fileID: 2800000, guid: 49bae1b5654b5eb47bfb222f8cf6d5ff, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _OcclusionMap:
        m_Texture: {fileID: 2800000, guid: 5824ed9b25c174049a3b172f032215ba, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _SubsurfaceMaskMap:
        m_Texture: {fileID: 2800000, guid: 3be3a74c5ab09424ab2ab3a8bc46877b, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _Texture2DAsset_74e06b99298a4bb39b9723942e5c00d2_Out_0_Texture2D:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _ThicknessMap:
        m_Texture: {fileID: 2800000, guid: 5e86db9598ef3e74cb80b70958f47293, type: 3}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - _texcoord:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_Lightmaps:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_LightmapsInd:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    - unity_ShadowMasks:
        m_Texture: {fileID: 0}
        m_Scale: {x: 1, y: 1}
        m_Offset: {x: 0, y: 0}
    m_Ints: []
    m_Floats:
    - Vector1_7A26C0DF: 32
    - Vector1_8CA290E3: 0
    - Vector1_A208836A: 0.7
    - _AlphaCutoff: 0.5
    - _BumpScale: 1
    - _DetailNormalMapScale: 0.5
    - _EnableAnimation: 0
    - _EnvironmentReflections: 1
    - _Metallic: 1
    - _OcclusionStrength: 1
    - _QueueControl: 0
    - _QueueOffset: 0
    - _ReceiveShadows: 1
    - _Smoothness: 0.625
    - _SpecularHighlights: 1
    - _SubsurfaceMask: 0.34
    - _Thickness: 0.1
    - _TransAmbient: 0.1
    - _TransDirect: 0.9
    - _TransNormal: 0.95
    - _TransScattering: 2
    - _TransShadow: 0.5
    - _TransStrength: 1.5
    - _TransmissionShadow: 0.5
    - _XRMotionVectorsPass: 1
    m_Colors:
    - Color_99F9ED08: {r: 1, g: 1, b: 1, a: 0}
    - Color_ABD0D7B1: {r: 0.5943396, g: 0.5943396, b: 0.5943396, a: 0}
    - Color_DF55D64F: {r: 8, g: 8, b: 8, a: 0}
    - Color_FB0ECDA7: {r: 1, g: 1, b: 1, a: 0}
    - _BaseColor: {r: 0.99999994, g: 0.99999994, b: 0.99999994, a: 1}
    - _EmissionColor: {r: 0, g: 0, b: 0, a: 1}
    - _EmissiveColor: {r: 0, g: 0, b: 0, a: 0}
    - _SubsurfaceFalloff: {r: 1, g: 1, b: 1, a: 1}
  m_BuildTextureStacks: []
  m_AllowLocking: 1
