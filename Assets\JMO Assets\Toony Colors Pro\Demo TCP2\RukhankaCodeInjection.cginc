/// Rukhanka & DOTS Code Injection Example for TCP2 (2025 Integration Guide Compliant)
/// This file demonstrates robust Rukhanka/DOTS integration for TCP2-generated shaders.
/// Place this file in the CODE INJECTION tab of the Shader Generator UI.
///
/// Follows best practices from the 2025 Rukhanka URP Shader Integration Guide.

// -----------------------------------------------------------------------------
// BLOCK: Rukhanka & DOTS Properties
//# Inject @ Properties/Start
[HideInInspector]_DeformedMeshIndex("Deformed Mesh Buffer Index Offset", Float) = 0
[HideInInspector]_DeformationParamsForMotionVectors("Deformation Parameters", Vector) = (0,0,0,0) // Must be type Vector
[Toggle(INJECT_RUKHANKA_DEBUG)] _RukhankaDebug ("Enable Rukhanka Debug", Float) = 0.0
_RukhankaDebugColor ("Deform Debug Color", Color) = (1,0,0,1)
[TCP2Separator]

// -----------------------------------------------------------------------------
// BLOCK: Declare Custom CBUFFER Variables
//# Inject @ Variables/Inside CBuffer
half4 _RukhankaDebugColor;

// -----------------------------------------------------------------------------
// BLOCK: DOTS Instancing Macros & Shader Features
//# Inject @ Main Pass/Pragma
#pragma multi_compile_instancing
#pragma multi_compile _ DOTS_INSTANCING_ON
#pragma shader_feature INJECT_RUKHANKA_DEBUG

// -----------------------------------------------------------------------------
// BLOCK: DOTS Instancing Metadata for Rukhanka
//# Inject @ Main Pass/Pragma
#if defined(DOTS_INSTANCING_ON) || defined(UNITY_DOTS_INSTANCING_ENABLED)
    UNITY_DOTS_INSTANCING_START(MaterialPropertyMetadata)
        UNITY_DOTS_INSTANCED_PROP_OVERRIDE_SUPPORTED(float, _DeformedMeshIndex)
        UNITY_DOTS_INSTANCED_PROP_OVERRIDE_SUPPORTED(float4, _DeformationParamsForMotionVectors)
    UNITY_DOTS_INSTANCING_END(MaterialPropertyMetadata)
#endif

// -----------------------------------------------------------------------------
// BLOCK: Rukhanka Deformation Include
//# Inject @ Main Pass/Pragma
#include "Packages/com.rukhanka.animation/Rukhanka.Runtime/Deformation/Resources/ComputeDeformedVertex.hlsl"

// -----------------------------------------------------------------------------
// BLOCK: Custom Deformation Debug Logic
//# Inject @ Main Pass/Surface Function/End
#if defined(INJECT_RUKHANKA_DEBUG)
    // Example: Tint output albedo for debug visualization
    output.Albedo.rgb += _RukhankaDebugColor.rgb * 0.5;
#endif

// -----------------------------------------------------------------------------
// BLOCK: Extra Rukhanka Deformation (Advanced Users)
//# Inject @ Main Pass/Surface Function/End
#if defined(INJECT_RUKHANKA_EXTRA_DEFORM)
    // Example: apply additional offset to deformedPositionOS (requires variable to be in scope)
    // deformedPositionOS += float3(0, sin(_Time.y), 0) * 0.01;
#endif

/// Usage:
/// 1. Add this file as a code injection in TCP2's CODE INJECTION tab.
/// 2. The required Rukhanka properties, DOTS macros, and metadata will be injected in all generated shaders.
/// 3. Enable INJECT_RUKHANKA_DEBUG on your material to see debug color.
/// 4. Expand with additional blocks as needed for your custom Rukhanka/DOTS logic.
/// 5. Follow the Rukhanka URP Shader Integration Guide for further troubleshooting and advanced scenarios.
