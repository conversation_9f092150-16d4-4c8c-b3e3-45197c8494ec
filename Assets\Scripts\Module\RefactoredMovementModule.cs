using System;
using System.Collections;
using System.Collections.Generic;
using System.Threading;
using DefaultNamespace.Mono.Interface;
using DG.Tweening;
using Events;
using Mono.Extensions;
using Unity.Mathematics;
using UnityEngine;

namespace Module.Mono.Animancer.RealsticFemale
{
    /// <summary>
    /// Refactored MovementModule that uses event-driven communication instead of direct animation calls.
    /// This module focuses purely on movement logic and delegates animation concerns to the event system.
    /// </summary>
    public class RefactoredMovementModule : MonoBehaviour, IModule<MovementSubState>, IUpdateSubModule<MovementSubState>,
        IUpdateState<AnimationSubState>
    {
        #region Properties and Fields

        public bool CanUpdate { get; private set; }

        [Header("Movement Configuration")]
        [Tooltip("Enable improved movement for smoother direction changes")]
        [SerializeField] private bool improvedMovement = false;
        [Tooltip("Use root motion for turning animations")]
        [SerializeField] private bool useRootMotionForTurning = false;

        [Header("Movement Parameters")]
        [SerializeField] private float moveSpeed = 5f;
        [SerializeField] private float rotationSpeed = 180f;
        [SerializeField] private float aimingSpeedMultiplier = 0.6f;
        [SerializeField] private float strafingSpeedMultiplier = 0.8f;
        [SerializeField] private float backpedalSpeedMultiplier = 0.7f;

        [Header("Rotation Settings")]
        [SerializeField] private bool m_useWholeBodyRotation = false;
        [SerializeField] private float rotationThreshold = 45f;

        [Header("Debug Settings")]
        [SerializeField] private bool enableDebugLogging = false;

        // Movement state
        private Vector3 currentVelocity = Vector3.zero;
        private Vector3 lastInputDirection = Vector3.zero;
        private float currentSpeed = 0f;
        private float velocityChangeSpeed = 0f;
        private bool isMoving = false;

        // Rotation state
        private float lastInputAngle = 0f;
        private bool m_changeDirection = false;
        private bool m_waitForChangeDirection = false;
        private bool m_isTurningBack = false;
        private Tweener rotationTweener;

        // Component references
        private Transform m_playerTransform;
        private CharacterParameters characterParameters;

        // Module state
        [field: SerializeField] public int ControllerIndex { get; private set; }
        [field: SerializeField] public List<MainState> MainState { get; private set; }
        [SerializeField] private MovementSubState _moduleSubState;
        [SerializeField] private AnimationSubState _subState;

        // Task management
        private string _stateName;
        private CancellationTokenSource _cancellationTokenSource;

        [SerializeField] private float m_horizontal;
        [SerializeField] private float m_vertical;
        [SerializeField] private float m_inputAngle;
        [SerializeField] private float m_inputMagnitude;
        [SerializeField] private bool m_isAiming;
        [SerializeField] private Vector3 m_AimDirection;

        #endregion

        #region Properties

        public MovementSubState SubState
        {
            get { return _moduleSubState; }
            set
            {
                var previousState = _moduleSubState;
                _moduleSubState = value;

                // Update StateManager
                PlayerController.Instance.StateManager.CurrentSubState = _moduleSubState;

                // Broadcast movement state change event instead of direct animation calls
                BroadcastMovementStateChange(previousState, value);
            }
        }

        public void SetState()
        {
            throw new NotImplementedException();
        }

        public void SetSubModule()
        {
            throw new NotImplementedException();
        }

        AnimationSubState IUpdateState<AnimationSubState>.SubState
        {
            get { return _subState; }
            set { _subState = value; }
        }

        public bool IsMoving
        {
            get { return isMoving; }
            private set
            {
                if (isMoving != value)
                {
                    isMoving = value;
                    LogDebug($"Movement state changed: IsMoving = {isMoving}");
                }
            }
        }

        #endregion

        #region Unity Lifecycle

        private void Awake()
        {
            // Initialize component references
            if (m_playerTransform == null)
                m_playerTransform = transform;

            // Initialize state
            CanUpdate = true;
            _moduleSubState = MovementSubState.Standing;

            LogDebug("RefactoredMovementModule initialized");
        }

        private void Start()
        {
            // Subscribe to events
            SubscribeToEvents();

            // Initialize with standing state
            BroadcastMovementStateChange(MovementSubState.Standing, MovementSubState.Standing);
        }

        private void OnDestroy()
        {
            // Cleanup
            UnsubscribeFromEvents();
            CancelAllTasks();

            if (rotationTweener != null && rotationTweener.IsActive())
                rotationTweener.Kill();
        }

        #endregion

        #region Event Management

        /// <summary>
        /// Subscribe to relevant events
        /// </summary>
        private void SubscribeToEvents()
        {
            EventManager.Subscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Subscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
            EventManager.Subscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Subscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Subscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);

            LogDebug("Subscribed to movement events");
        }

        /// <summary>
        /// Unsubscribe from events
        /// </summary>
        private void UnsubscribeFromEvents()
        {
            EventManager.Unsubscribe<OnChangeDirectionEvent>(OnChangeDirection);
            EventManager.Unsubscribe<OnCharacterAnimationFinishedEvent>(OnCharacterAnimationFinished);
            EventManager.Unsubscribe<OnDetectTargetEvent>(OnTargetDetected);
            EventManager.Unsubscribe<OnLostTargetEvent>(OnTargetLost);
            EventManager.Unsubscribe<OnRequireFastRotationEvent>(OnRequireFastRotation);

            LogDebug("Unsubscribed from movement events");
        }

        /// <summary>
        /// Broadcast movement state change event instead of calling animations directly
        /// </summary>
        private void BroadcastMovementStateChange(MovementSubState previousState, MovementSubState newState)
        {
            if (previousState == newState && previousState != MovementSubState.Standing)
                return;

            var movementEvent = new OnMovementStateChangedEvent(
                previousState,
                newState,
                m_inputMagnitude,
                GetCurrentInputDirection(),
                IsMoving
            );

            EventManager.Broadcast(movementEvent);
            LogDebug($"Broadcasted movement state change: {previousState} → {newState}");
        }

        /// <summary>
        /// Broadcast animation change request for complex animation scenarios
        /// </summary>
        private void BroadcastAnimationChangeRequest(MovementSubState movementState, bool forceTransition = false, float customDuration = -1f)
        {
            var animationRequest = new OnAnimationChangeRequestEvent(
                movementState,
                WeaponSubState.Idle, // Default weapon state
                WeaponSubModuleState.EmptyHand,
                m_isAiming,
                false, // Not shooting from movement
                m_inputMagnitude,
                GetCurrentInputDirection(),
                forceTransition,
                customDuration
            );

            EventManager.Broadcast(animationRequest);
            LogDebug($"Broadcasted animation change request: {movementState}");
        }

        #endregion

        #region Event Handlers

        /// <summary>
        /// Handle direction change events
        /// </summary>
        private void OnChangeDirection(OnChangeDirectionEvent eventData)
        {
            LogDebug($"Direction change event received: IsAimToNormal = {eventData.IsAimToNormal}");

            m_changeDirection = true;
            m_waitForChangeDirection = true;

            // Handle direction change logic without direct animation calls
            if (eventData.IsAimToNormal)
            {
                // Transitioning from aim to normal movement
                HandleAimToNormalTransition();
            }
            else
            {
                // Transitioning from normal to aim movement
                HandleNormalToAimTransition();
            }
        }

        /// <summary>
        /// Handle character animation finished events
        /// </summary>
        private void OnCharacterAnimationFinished(OnCharacterAnimationFinishedEvent eventData)
        {
            LogDebug($"Animation finished: {eventData.MovementSubState}, Length: {eventData.AnimationLength}");

            // Handle post-animation logic based on the finished animation
            switch (eventData.MovementSubState)
            {
                case MovementSubState.WalkingStart:
                    HandleWalkingStartFinished();
                    break;
                case MovementSubState.Stop:
                    HandleStopFinished();
                    break;
                case MovementSubState.InPositionRotation:
                    HandleInPositionRotationFinished();
                    break;
            }
        }

        /// <summary>
        /// Handle target detection events
        /// </summary>
        private void OnTargetDetected(OnDetectTargetEvent eventData)
        {
            LogDebug($"Target detected at: {eventData.TargetPosition}");
            // Movement module can adjust behavior when targets are detected
            // but doesn't directly handle animations
        }

        /// <summary>
        /// Handle target lost events
        /// </summary>
        private void OnTargetLost(OnLostTargetEvent eventData)
        {
            LogDebug("Target lost - forcing stop and stand");
            ForceStopAndStand();
        }

         public void ForceStopAndStand()
        {
            Debug.Log("[MovementModule] Forcing Stop -> Standing due to target loss.");
            IsMoving = false;
            SubState = MovementSubState.Stop;
            //ToStop();
            // After stop, transition to standing
            StartCoroutine(WaitAndSetStanding());
        }



         /// <summary>
        /// Handle fast rotation requests
        /// </summary>
        private void OnRequireFastRotation(OnRequireFastRotationEvent eventData)
        {
            LogDebug($"Fast rotation requested towards: {eventData.TargetPosition}");
            PerformFastRotation(eventData.TargetPosition);
        }

        #endregion

        #region Core Movement Logic

        public void Initialize(int controllerIndex)
        {
            ControllerIndex = controllerIndex;

            lastInputAngle = characterParameters?.InputAngle.Value ?? 0f;

            // Broadcast initial state instead of calling animation directly
            BroadcastMovementStateChange(MovementSubState.Standing, MovementSubState.Standing);

            LogDebug($"MovementModule initialized with controller index: {controllerIndex}");
        }

        /// <summary>
        /// Main module update method - focuses purely on movement logic
        /// </summary>
        public void UpdateModule(MainState currentMainState, ref Enum currentSubState)
        {
            if (!CanProcessInput())
                return;

            SubState = (MovementSubState)currentSubState;

            // Get current input values
            float currentInputAngle = characterParameters.InputAngle.Value;
            float inputMagnitude = characterParameters.InputMagnitude.Value;

            // Handle direction changes
            if (m_changeDirection)
                return;

            // Apply whole body rotation if needed (without animation calls)
            if (characterParameters.IsAiming && m_useWholeBodyRotation)
            {
                UpdateCharacterRotation();
            }

            // Process movement state transitions using events
            ProcessMovementStateTransitions(inputMagnitude);

            // Update current substate reference
            currentSubState = SubState;
        }

        /// <summary>
        /// Process movement state transitions without direct animation calls
        /// </summary>
        private void ProcessMovementStateTransitions(float inputMagnitude)
        {
            switch (SubState)
            {
                case MovementSubState.Standing:
                    HandleStandingState(inputMagnitude);
                    break;

                case MovementSubState.WalkingStart:
                    HandleWalkingStartState(inputMagnitude);
                    break;

                case MovementSubState.WalkingWithTurn:
                    HandleWalkingWithTurnState(inputMagnitude);
                    break;

                case MovementSubState.Stop:
                    HandleStopState(inputMagnitude);
                    break;

                case MovementSubState.InPositionRotation:
                    HandleInPositionRotationState(inputMagnitude);
                    break;
            }
        }

        /// <summary>
        /// Handle standing state logic
        /// </summary>
        private void HandleStandingState(float inputMagnitude)
        {
            // Broadcast standing state for animation system
            BroadcastAnimationChangeRequest(MovementSubState.Standing);

            // Check for movement initiation
            if (inputMagnitude > 0.2f && !IsMoving)
            {
                InitiateMovement();
            }
        }

        /// <summary>
        /// Handle walking start state logic
        /// </summary>
        private void HandleWalkingStartState(float inputMagnitude)
        {
            if (inputMagnitude > 0.2f && IsMoving)
            {
                // Continue with walking start
                BroadcastAnimationChangeRequest(MovementSubState.WalkingStart);
                ProcessWalkingStart();
            }
            else if (inputMagnitude < 0.2f)
            {
                // Stop movement
                InitiateStop();
            }
        }

        /// <summary>
        /// Handle walking with turn state logic
        /// </summary>
        private void HandleWalkingWithTurnState(float inputMagnitude)
        {
            if (inputMagnitude > 0.2f && IsMoving)
            {
                // Continue walking with turning
                ProcessWalkingWithTurning();
            }
            else if (inputMagnitude < 0.2f)
            {
                // Stop movement
                InitiateStop();
            }
        }

        /// <summary>
        /// Handle stop state logic
        /// </summary>
        private void HandleStopState(float inputMagnitude)
        {
            // Stop state is handled by animation events
            // Movement module just waits for animation completion
        }

        /// <summary>
        /// Handle in-position rotation state logic
        /// </summary>
        private void HandleInPositionRotationState(float inputMagnitude)
        {
            // In-position rotation is handled by animation events
            // Movement module manages the rotation logic
        }

        #endregion

        #region Movement State Handlers

        /// <summary>
        /// Initiate movement from standing
        /// </summary>
        private void InitiateMovement()
        {
            IsMoving = true;

            // Cancel any previous tasks
            CancelPreviousTask("Standing");

            // Schedule transition to walking start using events
            _cancellationTokenSource = new CancellationTokenSource();
            _stateName = "Standing";

            this.WaitForSecondsAsync("Standing", 0.04f, () =>
            {
                SubState = MovementSubState.WalkingStart;
                BroadcastAnimationChangeRequest(MovementSubState.WalkingStart);
            }, _cancellationTokenSource.Token);

            LogDebug("Movement initiated: Standing → WalkingStart");
        }

        /// <summary>
        /// Process walking start without direct animation calls
        /// </summary>
        private void ProcessWalkingStart()
        {
            // Apply movement physics
            ApplyMovement(characterParameters.Horizontal.Value, characterParameters.Vertical.Value);

            // Schedule transition to walking with turn
            CancelPreviousTask("WalkStart");
            _cancellationTokenSource = new CancellationTokenSource();
            _stateName = "WalkStart";

            this.WaitForSecondsAsync("WalkStart", 1f, () =>
            {
                if (SubState == MovementSubState.Stop)
                    return;

                // Transition to walking with turn using events
                SubState = MovementSubState.WalkingWithTurn;
                BroadcastAnimationChangeRequest(MovementSubState.WalkingWithTurn);

                if (m_isTurningBack)
                    m_isTurningBack = false;

                m_waitForChangeDirection = false;

            }, _cancellationTokenSource.Token);
        }

        /// <summary>
        /// Process walking with turning without direct animation calls
        /// </summary>
        private void ProcessWalkingWithTurning()
        {
            // Apply movement physics
            ApplyMovement(characterParameters.Horizontal.Value, characterParameters.Vertical.Value);

            // Broadcast continuous walking state for animation updates
            BroadcastAnimationChangeRequest(MovementSubState.WalkingWithTurn);
        }

        /// <summary>
        /// Initiate stop movement
        /// </summary>
        private void InitiateStop()
        {
            IsMoving = false;
            SubState = MovementSubState.Stop;

            // Broadcast stop animation request
            BroadcastAnimationChangeRequest(MovementSubState.Stop);

            LogDebug("Movement stopped: → Stop");
        }

        #endregion

        #region Animation Event Handlers

        /// <summary>
        /// Handle walking start animation finished
        /// </summary>
        private void HandleWalkingStartFinished()
        {
            if (SubState != MovementSubState.Stop && IsMoving)
            {
                SubState = MovementSubState.WalkingWithTurn;
                BroadcastAnimationChangeRequest(MovementSubState.WalkingWithTurn);
                LogDebug("Walking start finished → WalkingWithTurn");
            }
        }

        /// <summary>
        /// Handle stop animation finished
        /// </summary>
        private void HandleStopFinished()
        {
            SubState = MovementSubState.Standing;
            BroadcastAnimationChangeRequest(MovementSubState.Standing);
            LogDebug("Stop finished → Standing");
        }

        /// <summary>
        /// Handle in-position rotation finished
        /// </summary>
        private void HandleInPositionRotationFinished()
        {
            SubState = MovementSubState.Standing;
            BroadcastAnimationChangeRequest(MovementSubState.Standing);
            LogDebug("In-position rotation finished → Standing");
        }

        /// <summary>
        /// Handle aim to normal transition
        /// </summary>
        private void HandleAimToNormalTransition()
        {
            // Logic for transitioning from aiming to normal movement
            // Uses events instead of direct animation calls
            LogDebug("Handling aim to normal transition");

            if (IsMoving)
            {
                BroadcastAnimationChangeRequest(SubState, true, 0.25f);
            }
        }

        /// <summary>
        /// Handle normal to aim transition
        /// </summary>
        private void HandleNormalToAimTransition()
        {
            // Logic for transitioning from normal to aiming movement
            // Uses events instead of direct animation calls
            LogDebug("Handling normal to aim transition");

            if (IsMoving)
            {
                BroadcastAnimationChangeRequest(SubState, true, 0.25f);
            }
        }

        #endregion

        #region Movement Physics and Utilities

        /// <summary>
        /// Apply movement physics without animation calls
        /// </summary>
        private void ApplyMovement(float horizontalInput, float verticalInput)
        {
            Vector3 inputDirection = new Vector3(horizontalInput, 0, verticalInput);
            float inputMagnitude = inputDirection.magnitude;

            // Normalize only if magnitude > 1 to preserve partial inputs
            if (inputMagnitude > 1f)
                inputDirection.Normalize();
            else if (inputMagnitude < 0.01f)
                inputDirection = Vector3.zero;

            // Calculate target speed with modifiers
            float targetSpeed = moveSpeed;
            bool isAiming = characterParameters.IsAiming;

            // Apply speed modifiers
            if (isAiming)
                targetSpeed *= aimingSpeedMultiplier;

            // Apply strafing and backpedal modifiers
            if (isAiming && Mathf.Abs(horizontalInput) > 0.7f)
                targetSpeed *= strafingSpeedMultiplier;
            else if (isAiming && verticalInput < -0.7f)
                targetSpeed *= backpedalSpeedMultiplier;

            // Calculate movement vector
            Vector3 movementVector = inputDirection * targetSpeed * Time.deltaTime;

            // Store current velocity for other systems
            currentVelocity = movementVector / Time.deltaTime;
            currentSpeed = currentVelocity.magnitude;
            lastInputDirection = inputDirection;

            // Apply movement
            if (movementVector.magnitude > 0)
            {
                m_playerTransform.position += movementVector;

                // Handle rotation (without animation calls)
                HandleMovementRotation(inputDirection, isAiming, inputMagnitude);
            }
        }

        /// <summary>
        /// Handle character rotation during movement
        /// </summary>
        private void HandleMovementRotation(Vector3 inputDirection, bool isAiming, float inputMagnitude)
        {
            // Only apply normal rotation if we're not in the middle of a fast rotation
            // and not aiming (aiming should prioritize looking at targets)
            if ((rotationTweener == null || !rotationTweener.IsActive()) && !isAiming)
            {
                // Calculate rotation speed based on input magnitude
                float adaptiveRotationSpeed = rotationSpeed * Mathf.Lerp(0.5f, 1.0f, inputMagnitude);

                // Rotate the character with smooth interpolation
                Quaternion targetRotation = Quaternion.LookRotation(inputDirection);
                m_playerTransform.rotation = Quaternion.RotateTowards(
                    m_playerTransform.rotation,
                    targetRotation,
                    adaptiveRotationSpeed * Time.deltaTime
                );
            }
        }

        /// <summary>
        /// Update character rotation for whole body rotation
        /// </summary>
        private void UpdateCharacterRotation()
        {
            // Whole body rotation logic without animation calls
            if (characterParameters.IsAiming)
            {
                Vector3 aimDirection = characterParameters.AimTarget;
                if (aimDirection != Vector3.zero)
                {
                    Quaternion targetRotation = Quaternion.LookRotation(aimDirection);
                    m_playerTransform.rotation = Quaternion.RotateTowards(
                        m_playerTransform.rotation,
                        targetRotation,
                        rotationSpeed * Time.deltaTime
                    );
                }
            }
        }

        /// <summary>
        /// Perform fast rotation towards target
        /// </summary>
        private void PerformFastRotation(Vector3 targetPosition)
        {
            Vector3 direction = (targetPosition - m_playerTransform.position).normalized;
            direction.y = 0; // Keep rotation on Y-axis only

            if (direction != Vector3.zero)
            {
                Quaternion targetRotation = Quaternion.LookRotation(direction);

                // Use DOTween for smooth fast rotation
                if (rotationTweener != null && rotationTweener.IsActive())
                    rotationTweener.Kill();

                rotationTweener = m_playerTransform.DORotateQuaternion(targetRotation, 0.3f)
                    .SetEase(Ease.OutCubic)
                    .OnComplete(() =>
                    {
                        LogDebug("Fast rotation completed");
                        // Broadcast rotation completion if needed
                    });
            }
        }

        /// <summary>
        /// Check if input can be processed
        /// </summary>
        private bool CanProcessInput()
        {
            return CanUpdate && characterParameters != null;
        }

        /// <summary>
        /// Get current input direction
        /// </summary>
        private Vector3 GetCurrentInputDirection()
        {
            if (characterParameters == null)
                return Vector3.zero;

            return new Vector3(
                characterParameters.Horizontal.Value,
                0,
                characterParameters.Vertical.Value
            );
        }

        /// <summary>
        /// Wait and set standing state
        /// </summary>
        private IEnumerator WaitAndSetStanding()
        {
            yield return new WaitForSeconds(0.15f);
            SubState = MovementSubState.Standing;
            BroadcastAnimationChangeRequest(MovementSubState.Standing);
            LogDebug("Transitioned to Standing after Stop");
        }

        #endregion

        #region Task Management

        /// <summary>
        /// Cancel previous task by name
        /// </summary>
        private void CancelPreviousTask(string taskName)
        {
            if (_stateName == taskName && _cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
                LogDebug($"Cancelled previous task: {taskName}");
            }
        }

        /// <summary>
        /// Cancel all active tasks
        /// </summary>
        private void CancelAllTasks()
        {
            if (_cancellationTokenSource != null)
            {
                _cancellationTokenSource.Cancel();
                _cancellationTokenSource.Dispose();
                _cancellationTokenSource = null;
                LogDebug("Cancelled all tasks");
            }
        }

        #endregion

        #region Debug and Logging

        /// <summary>
        /// Log debug message if logging is enabled
        /// </summary>
        private void LogDebug(string message)
        {
            if (enableDebugLogging)
            {
                Debug.Log($"[RefactoredMovementModule] {message}");
            }
        }

        #endregion

        #region Interface Implementations

        /// <summary>
        /// Get module substate
        /// </summary>
        public Enum GetModuleSubState()
        {
            return SubState;
        }

        /// <summary>
        /// Check if module has refresh rate
        /// </summary>
        public bool HasRefreshRate => false;

        /// <summary>
        /// Get refresh rate (not used in this implementation)
        /// </summary>
        public float RefreshRate => 0f;

        #endregion
    }
}
